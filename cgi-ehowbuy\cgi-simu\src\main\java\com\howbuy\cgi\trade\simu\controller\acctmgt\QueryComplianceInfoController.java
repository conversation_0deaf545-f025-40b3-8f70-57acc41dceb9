/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.controller.acctmgt;

import com.howbuy.acccenter.common.enums.ExamTypeEnum;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoFacade;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoRequest;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoResponse;
import com.howbuy.cgi.aspect.controller.AbstractCGIController;
import com.howbuy.cgi.common.CGIConstants;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.trade.simu.model.dto.QueryComplianceStateDto;
import com.howbuy.cgi.trade.simu.model.vo.CustomerComplianceStateVo;
import com.howbuy.cgi.trade.simu.model.vo.QueryFormTemplateVo;
import com.howbuy.cgi.trade.simu.model.vo.SubmitFormResponseVo;
import com.howbuy.cgi.trade.simu.model.vo.SubmitFormVo;
import com.howbuy.cgi.trade.simu.service.relatedaccount.AccCenterService;
import com.howbuy.kyc.common.RiskTestUtil;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.trade.common.basecommon.remote.DisCodeInvokerUtils;
import com.howbuy.trade.common.session.model.TradeSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 查询合规信息
 *
 * <AUTHOR>
 * @date 2021/5/24 14:29
 * @since JDK 1.8
 */
@Controller
public class QueryComplianceInfoController extends AbstractCGIController {

    /**
     * 私募用户的kyc信息
     */
    private static final String SIMU_KYC_INFO = "SIMU_KYC_INFO_NEW";
    @Autowired
    @Qualifier("simu.kycInfoFacade")
    private KycInfoFacade kycInfoFacade;
    @Autowired
    private AccCenterService accCenterService;

    /**
     * @api {POST} /simu/acctmgt/querycompliancestate.htm 查询合规状态
     * @apiVersion 1.0.0
     * @apiGroup SIMU-CGI
     * @apiName /simu/acctmgt/querycompliancestate.htm
     * @apiDescription 查询合规状态
     * @apiSuccess (响应结果) {String} elecSignFlag 电子签名约定书签署标识 0-未签；1-已签
     * @apiSuccess (响应结果) {String} signFlag 私募合格投资者承诺书签署状态： 0-未签署；1-已签署
     * @apiSuccess (响应结果) {String} riskFlag 客户风险评测状态 0-未评测；1-已评测未过期；2-已过期
     * @apiSuccessExample 响应结果示例
     * {"signFlag":"piZohBhsH","elecSignFlag":"3jR1ZY","riskFlag":"fhE"}
     */
    @RequestMapping("/simu/acctmgt/querycompliancestate.htm")
    private void queryComplianceInfo(HttpServletRequest request, HttpServletResponse response) throws IOException {
        TradeSession loginInfo = this.getCustSession();

        KycInfoResponse kycInfoResponse = (KycInfoResponse) request.getSession().getAttribute(SIMU_KYC_INFO);

        if (null != request.getParameter("reloadKyc")) {
            kycInfoResponse = null;
        }

        if (null == kycInfoResponse || !loginInfo.getUser().getHboneNo().equals(kycInfoResponse.getHboneNo())) {
            KycInfoRequest kycInfoRequest = new KycInfoRequest();
            kycInfoRequest.setHboneNo(loginInfo.getUser().getHboneNo());
            DisCodeInvokerUtils.setCommonParameters(kycInfoRequest);
            kycInfoResponse = kycInfoFacade.execute(kycInfoRequest);
        }

        if (null != kycInfoResponse && "0000000".equals(kycInfoResponse.getReturnCode())) {
            String elecSignFlag;
            String signFlag;
            String riskFlag;
            String riskLevel = null;

            // 私募/资管合格投资者承诺书签署状态
            if (!YesOrNoEnum.YES.getCode().equals(kycInfoResponse.getSignFlag()) || !YesOrNoEnum.YES.getCode().equals(kycInfoResponse.getFundFlag())) {
                signFlag = YesOrNoEnum.NO.getCode();
            } else {
                signFlag = YesOrNoEnum.YES.getCode();
            }

            // 电子签名约定书签署标识
            if (null == kycInfoResponse.geteSignatureConfirmation() || !kycInfoResponse.geteSignatureConfirmation()) {
                elecSignFlag = YesOrNoEnum.NO.getCode();
            } else {
                elecSignFlag = YesOrNoEnum.YES.getCode();
            }

            // 风险评测
            if (ExamTypeEnum.HIGH_END.getValue().equals(kycInfoResponse.getRiskToleranceExamType()) || ExamTypeEnum.INSTITUTION.getValue().equals(kycInfoResponse.getRiskToleranceExamType())) {
                if (!StringUtils.isBlank(kycInfoResponse.getRiskToleranceLevel())) {
                    riskLevel = RiskTestUtil.userRiskMap.get(kycInfoResponse.getRiskToleranceLevel());
                }
                if (kycInfoResponse.getRiskToleranceExpire() == null || kycInfoResponse.getRiskToleranceExpire()) {
                    // 风险评测到期
                    riskFlag = "2";
                } else {
                    // 风险评测有效
                    riskFlag = "1";
                }
            } else {
                // 未做过风险评测
                riskFlag = "0";
            }
            QueryComplianceStateDto dto = new QueryComplianceStateDto();
            dto.setElecSignFlag(elecSignFlag);
            dto.setSignFlag(signFlag);
            dto.setRiskFlag(riskFlag);
            dto.setRiskLevel(riskLevel);
            write(dto, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
        } else {
            throw new BizException(BizErrorEnum.BUSY_ERROR.getCode(), BizErrorEnum.BUSY_ERROR.getDesc());
        }
    }


    /**
     * @api {GET} /simu/acctmgt/queryComplianceStateV2.htm queryComplianceStateV2
     * @apiVersion 1.0.0
     * @apiGroup QueryComplianceInfoController
     * @apiName queryComplianceStateV2
     * @apiDescription 合规融合页
     * @apiParam {String} [disCode] 分销渠道:HB000A001 好买; HZ000N001 好臻
     * @apiSuccess (响应结果) {Array} complianceStateInfoList 合规信息
     * @apiSuccess (响应结果) {Object} complianceStateInfoList.queryComplianceStateDto 合规信息
     * @apiSuccess (响应结果) {String} complianceStateInfoList.queryComplianceStateDto.elecSignFlag 电子签名约定书签署标识 0-未签；1-已签
     * @apiSuccess (响应结果) {String} complianceStateInfoList.queryComplianceStateDto.signFlag 私募合格投资者承诺书签署状态： 0-未签署；1-已签署
     * @apiSuccess (响应结果) {String} complianceStateInfoList.queryComplianceStateDto.riskFlag 客户风险评测状态 0-未评测；1-已评测未过期；2-已过期
     * @apiSuccess (响应结果) {String} complianceStateInfoList.queryComplianceStateDto.riskLevel 客户风险等级 0,1,2,3,4,5 没有做过的情况下为null
     * @apiSuccess (响应结果) {String} complianceStateInfoList.queryComplianceStateDto.investorType 投资者类型,专业:PRO;普通:NORMAL
     * @apiSuccess (响应结果) {String} complianceStateInfoList.disCode 分销渠道
     * @apiSuccessExample 响应结果示例
     * {"complianceStateInfoList":[{"queryComplianceStateDto":{"signFlag":"Oxh","riskLevel":"8O7qENh","elecSignFlag":"Fgv8n8iK","riskFlag":"iMwfLJnHL","investorType":"y"},"disCode":"Qh08FzlC"}]}
     */
    @RequestMapping("/simu/acctmgt/queryComplianceStateV2.htm")
    private void queryComplianceStateV2(HttpServletRequest request, HttpServletResponse response) throws IOException {
        TradeSession loginInfo = this.getCustSession();
        // 获取入参
        String disCode = request.getParameter("disCode");
        CustomerComplianceStateVo customerComplianceStateVo = accCenterService.queryComplianceState(loginInfo.getUser().getHboneNo(), disCode);
        write(customerComplianceStateVo, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * @api {GET} /simu/acctmgt/queryQualifiedInvestorCertification.htm queryQualifiedInvestorCertification
     * @apiVersion 1.0.0
     * @apiGroup QueryComplianceInfoController
     * @apiName queryQualifiedInvestorCertification
     * @apiParam {String} fundCode 产品编码
     * @apiDescription 获取好买合格投资者认定
     * @apiSuccess (响应结果) {String} templateId 模版id
     * @apiSuccess (响应结果) {String} formContent 模版内容
     * @apiSuccessExample 响应结果示例
     * {"formContent":"9QGiFG","templateId":"5E"}
     */
    @RequestMapping("/simu/acctmgt/queryQualifiedInvestorCertification.htm")
    private void queryQualifiedInvestorCertification(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String fundCode = getString("fundCode");
        TradeSession loginInfo = this.getCustSession();
        QueryFormTemplateVo queryFormTemplateVo = accCenterService.queryQualifiedInvestorCertification(loginInfo, fundCode);
        write(queryFormTemplateVo, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }


    /**
     * @api {GET} /simu/acctmgt/submitQualifiedInvestorCertification.htm submitQualifiedInvestorCertification
     * @apiVersion 1.0.0
     * @apiGroup QueryComplianceInfoController
     * @apiName submitQualifiedInvestorCertification
     * @apiDescription 保存好买合格投资者认定内容
     * @apiParam (请求参数) {String} formContent 表单内容
     * @apiParam (请求参数) {String} templateId 表单模版id
     * @apiParamExample 请求参数示例
     * formContent=WJqW&templateId=qqejcLaG7
     * @apiSuccess (响应结果) {String} templateId 模版id
     * @apiSuccess (响应结果) {String} formNo 提交表单编号
     * @apiSuccessExample 响应结果示例
     * {"formNo":"LYjXXUQ","templateId":"MRf6wZs9G"}
     */
    @RequestMapping("/simu/acctmgt/submitQualifiedInvestorCertification.htm")
    private void submitQualifiedInvestorCertification(HttpServletRequest request, HttpServletResponse response) throws IOException {
        TradeSession loginInfo = this.getCustSession();
        SubmitFormVo submitFormVo = getCommand(SubmitFormVo.class);
        SubmitFormResponseVo submitFormResponseVo = accCenterService.submitQualifiedInvestorCertification(loginInfo, submitFormVo);
        write(submitFormResponseVo, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

}