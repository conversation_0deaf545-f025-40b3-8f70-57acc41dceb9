<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>


	<groupId>com.howbuy.tms</groupId>
	<artifactId>high-order-center-client</artifactId>
	<packaging>jar</packaging>
	<name>high-order-center-client</name>
	<version>4.8.88-RELEASE</version>
	<properties>
	    <com.howbuy.tms-common-client.version>4.8.37-RELEASE</com.howbuy.tms-common-client.version>
		<com.howbuy.tms-common-enums.version>4.8.37-RELEASE</com.howbuy.tms-common-enums.version>
		<lombok.version>1.18.14</lombok.version>
</properties>
	<dependencies>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-client</artifactId>
			<version>${com.howbuy.tms-common-client.version}</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-enums</artifactId>
			<version>${com.howbuy.tms-common-enums.version}</version>
		</dependency>
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.6</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<version>5.2.15.RELEASE</version>
			<scope>provided</scope>
		</dependency>
	</dependencies>

	<distributionManagement>
		<repository>
			<id>howbuy-release</id>
			<name>howbuy-release</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshot</id>
			<name>howbuy-snapshot</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>

</project>