package com.howbuy.cgi.trade.simu.service.task;

import com.howbuy.cgi.common.remote.param.RemoteParametersProvider;
import com.howbuy.cgi.trade.simu.util.OpsSysMonitor;
import com.howbuy.trace.RequestChainTrace;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * @Description:基础任务
 * @Author: yun.lu
 * Date: 2024/7/5 11:07
 */
public abstract class HowbuyBaseTask implements Runnable {
    private CountDownLatch latch;

    private String outCode;

    private String txChannel;

    private RequestAttributes requestAttributes;

    public HowbuyBaseTask() {
        this.outCode = RemoteParametersProvider.getOutletCode();
        this.txChannel = RemoteParametersProvider.getTradeChannel();
        this.requestAttributes = RequestContextHolder.getRequestAttributes();
    }

    public CountDownLatch getLatch() {
        return latch;
    }

    public void setLatch(CountDownLatch latch) {
        this.latch = latch;
    }

    @Override
    public void run() {
        String uuid = RequestChainTrace.getReqId();
        RequestChainTrace.buildAndSet(uuid, (String) null);
        // 获取主线程的 MDC 上下文并复制到当前线程
        Map<String, String> mdcContextMap = org.apache.logging.log4j.ThreadContext.getContext();
        org.apache.logging.log4j.ThreadContext.putAll(mdcContextMap);

        try {
            RequestContextHolder.setRequestAttributes(getRequestAttributes());
            callTask();
        } catch (Exception e) {
            OpsSysMonitor.warn("异步任务报异常,e:" + e, OpsSysMonitor.ERROR);
            throw e;
        } finally {
            if (latch != null) {
                latch.countDown();
            }
            // 清理当前线程的 MDC 上下文
            org.apache.logging.log4j.ThreadContext.clearAll();
        }

    }

    /**
     * 执行task方法
     */
    protected abstract void callTask();

    public String getOutCode() {
        return outCode;
    }

    public void setOutCode(String outCode) {
        this.outCode = outCode;
    }

    public String getTxChannel() {
        return txChannel;
    }

    public void setTxChannel(String txChannel) {
        this.txChannel = txChannel;
    }

    public RequestAttributes getRequestAttributes() {
        return requestAttributes;
    }

    public void setRequestAttributes(RequestAttributes requestAttributes) {
        this.requestAttributes = requestAttributes;
    }
}
