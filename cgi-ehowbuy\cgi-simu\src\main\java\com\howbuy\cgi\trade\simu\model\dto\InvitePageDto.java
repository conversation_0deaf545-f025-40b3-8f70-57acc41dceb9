package com.howbuy.cgi.trade.simu.model.dto;

import com.howbuy.cgi.trade.simu.model.dto.relatedaccount.InviteEntryDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:邀请页信息
 * @Author: yun.lu
 * Date: 2025/7/29 16:51
 */
@Data
public class InvitePageDto implements Serializable {
    /**
     * 首邀请列表
     */
    private List<InviteEntryDto> inviteEntryDtoList;
}
