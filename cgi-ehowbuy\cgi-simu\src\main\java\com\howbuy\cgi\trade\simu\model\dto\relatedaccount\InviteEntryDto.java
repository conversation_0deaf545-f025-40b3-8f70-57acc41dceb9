/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.model.dto.relatedaccount;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 邀请码信息
 *
 * <AUTHOR>
 * @date 2025/07/28
 * @since JDK 1.8
 */
@Getter
@Setter
public class InviteEntryDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 邀请ID
     */
    private String inviteId;

    /**
     * 邀请人姓名
     */
    private String inviterName;

    /**
     * 邀请时间:yyyyMMddHHmmss
     */
    private String inviterTime;

    /**
     * 邀请是否过期,0:未过期,1:已经过期
     */
    private String hasExpired;

    /**
     * 过期时间:yyyyMMddHHmmss
     */
    private String expireTime;

}
