package com.howbuy.cgi.trade.simu.controller.acctmgt;

import com.howbuy.cgi.aspect.controller.AbstractCGIController;
import com.howbuy.cgi.common.CGIConstants;
import com.howbuy.cgi.trade.simu.model.vo.AccountStatusInfo;
import com.howbuy.cgi.trade.simu.service.relatedaccount.AccCenterService;
import com.howbuy.trade.common.session.model.TradeSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description:账户信息相关接口
 * @Author: yun.lu
 * Date: 2024/1/24 15:57
 */
@Controller
public class AccountInfoController extends AbstractCGIController {
    @Autowired
    AccCenterService accCenterService;

    /**
     * @api {GET} /simu/acctmgt/getAccountStatusInfo.htm getAccountStatusInfo
     * @apiVersion 1.0.0
     * @apiGroup AccountInfoController
     * @apiName getAccountStatusInfo
     * @apiDescription 查询用户开户状态
     * @apiSuccess (响应结果) {String} hasHzAccount 是否有好臻账户,1:是;0:否
     * @apiSuccess (响应结果) {String} hasHzAccountActive 好臻账户是否已激活,1:是;0:否
     * @apiSuccess (响应结果) {String} hasHmAccount 是否有好买账户,1:是;0:否
     * @apiSuccess (响应结果) {String} hasHmAccountActive 好买账户是否已激活,1:是;0:否
     * @apiSuccessExample 响应结果示例
     * {"hasHmAccount":"jBR8JHDzMI","hasHzAccountActive":"RD4","hasHmAccountActive":"5WUW","hasHzAccount":"qUzKp2J6"}
     */
    @RequestMapping("/simu/acctmgt/getAccountStatusInfo.htm")
    public void getAccountStatusInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = this.getCustSession();
        AccountStatusInfo accountStatusInfo = accCenterService.getAccountStatusInfo(loginInfo.getUser().getHboneNo());
        write(accountStatusInfo, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }


}
