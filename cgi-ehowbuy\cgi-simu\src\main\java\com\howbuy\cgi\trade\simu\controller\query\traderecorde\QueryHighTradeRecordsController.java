package com.howbuy.cgi.trade.simu.controller.query.traderecorde;

import com.howbuy.cgi.common.CGIConstants;
import com.howbuy.cgi.common.util.CgiParamUtil;
import com.howbuy.cgi.trade.simu.controller.AbstractSimuCGIController;
import com.howbuy.cgi.trade.simu.model.cmd.QueryRecordsCmd;
import com.howbuy.cgi.trade.simu.model.dto.AcctDataAuthDto;
import com.howbuy.cgi.trade.simu.model.dto.MutiBankCardInfo;
import com.howbuy.cgi.trade.simu.model.dto.QueryRecordsDto;
import com.howbuy.cgi.trade.simu.model.dto.TradeFlowRspDTO;
import com.howbuy.cgi.trade.simu.service.DtmsService;
import com.howbuy.cgi.trade.simu.service.relatedaccount.AccCenterService;
import com.howbuy.interlayer.common.utils.DateUtils;
import com.howbuy.paramcenter.serverfacade.dis.queryhbdiscodelist.QueryHbDisCodeListFacade;
import com.howbuy.paramcenter.serverfacade.dis.queryhbdiscodelist.QueryHbDisCodeListRequest;
import com.howbuy.paramcenter.serverfacade.dto.DisDto;
import com.howbuy.paramcenter.vo.Result;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.ScaleTypeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.ProductTypeEnum;
import com.howbuy.tms.common.utils.MoneyUtil;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListFacade;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListRequest;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListResponse;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListResponse.DealOrderBean;
import com.howbuy.trade.account.service.account.UserService;
import com.howbuy.trade.common.session.model.TradeSession;
import com.howbuy.trade.common.session.model.UserInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Controller
public class QueryHighTradeRecordsController extends AbstractSimuCGIController {
    @Autowired
    @Qualifier("simu.queryDealOrderListFacade")
    private QueryDealOrderListFacade queryDealOrderListFacade;

    @Autowired
    @Qualifier("simu.queryHbDisCodeListFacade")
    private QueryHbDisCodeListFacade queryHbDisCodeListFacade;

    @Autowired
    private DtmsService dtmsService;

    @Autowired
    private AccCenterService accCenterService;

    // 查询起始页
    private static final int START_PAGE_NUM = 1;

    // 默认每页条目数
    private static final int DEFAULT_PAGE_SIZE = 20;

    // 业务码分割符
    private static final String BUSI_CODE_SPLIT = ",";

    // 好臻分销代码
    private static final String HZ_DISCODE = "HZ000N001";

    @Autowired
    private UserService userServiceImpl;

    /**
     * @api {get}  /simu/query/records.htm 查询交易记录列表
     * @apiGroup query
     * @apiName /simu/query/records.htm
     * @apiDescription 查询交易记录列表
     * @apiParam {String} targetPage 页码，默认为1
     * @apiParam {String} pageSize 每页显示条目数，默认为20
     * @apiParam {String} transType 交易业务码  1120-认购;1122-申购;1124-赎回; 1129-修改分红方式
     * @apiParam {String} transStatus 状态 1待确认 2 已撤单 3 交易成功 4 交易失败
     * @apiParam {String} beginDate 开始时间 格式yyyyMMdd
     * @apiParam {String} endDate 结束时间 格式yyyyMMdd
     * @apiParam {String} fundCodeOrNameOrAbbr 基金简称或代码
     * @apiParam {String} cpAcctNo 资金帐号
     * @apiSuccess {String} desc 返回描述
     * @apiSuccess {String} code 返回码
     * @apiSuccess {Object} body 数据
     * @apiSuccess (body) {int} currentPage 当前页码
     * @apiSuccess (body) {int} totalPage 总页码
     * @apiSuccess (body) {int} totalCount 总条目数
     * @apiSuccess (body) {String} hasHZProduct 是否持有好臻产品 0:没有,1:有
     * @apiSuccess (body) {String} hasHKProduct 是否持有好买香港产品  0:没有,1:有
     * @apiSuccess (body) {String} isAuth 是否授权  0:没有,1:有
     * @apiSuccess (body) {Array}  tradeRecordList 列表
     * @apiSuccess (tradeRecordList) {String} appSheetSerialNo 公募专户：订单号 私募：合同号
     * @apiSuccess (tradeRecordList) {String} fundCode 基金代码
     * @apiSuccess (tradeRecordList) {String} fundNameAbbr 基金名称
     * @apiSuccess (tradeRecordList) {String} time 交易时间
     * @apiSuccess (tradeRecordList) {BigDecimal} vol 申请份额
     * @apiSuccess (tradeRecordList) {BigDecimal} amount 申请金额
     * @apiSuccess (tradeRecordList) {BigDecimal} confirmVol 确认份额
     * @apiSuccess (tradeRecordList) {BigDecimal} confirmAmount 确认金额
     * @apiSuccess (tradeRecordList) {String} currency 币种
     * @apiSuccess (tradeRecordList) {String} payChannelCode 01-自划款; 04-代扣款; 06-储蓄罐
     * @apiSuccess (tradeRecordList) {BigDecimal} discountfee 无折扣手续费
     * @apiSuccess (tradeRecordList) {String} tradeFlag 中台业务码 1120-认购; 1122-申购; 1124-赎回; 1136-基金转换; 1236-基金转投; 1142-强赎; 1143-分红;1144-强增;1145-强减;1134-非交易过户转入;1135-非交易过户转出;1129-修改分红方式;1260-拉杆平衡;1261-波动平衡;1262-观点平衡；1999-股权回款
     * @apiSuccess (tradeRecordList) {String} divMode 分红方式 0-红利再投;1-现金分红
     * @apiSuccess (tradeRecordList) {String} finaDirect 赎回,分红，强赎的资金流向0-银行卡;1-储蓄罐
     * @apiSuccess (tradeRecordList) {String} tradeStatus 公募专户-订单状态;  1-申请成功;2-部分确认;3-确认成功;4-确认失败;5-自行撤销;6-强制取消
     * @apiSuccess (tradeRecordList) {String} payStatus 公募专户-支付状态;  0-无需付款;1-未付款;2-付款中;3-部分成功;4-成功;5-失败
     * @apiSuccess (tradeRecordList) {String} bankCode 银行code;
     * @apiSuccess (tradeRecordList) {String} bankAcct 银行账号;
     * @apiSuccess (tradeRecordList) {String} isMutiCardTradeFlag 多卡交易标识 0-否；1-是
     * @apiSuccess (tradeRecordList) {List}   mutiCardList 多卡交易列表
     * @apiSuccess (tradeRecordList) {String} isVolTansfer 是否份额结转 1是，0否
     * @apiSuccess (tradeRecordList) {String} mergeSubmitFlag 合并上报标识 1-合并上报
     * @apiSuccess (mutiCardList) {String} bankCode 银行代码
     * @apiSuccess (mutiCardList) {String} bankName 银行名称
     * @apiSuccess (mutiCardList) {String} bankAcct 银行账号
     * @apiSuccess (mutiCardList) {String} appAmt    交易金额
     */
    @RequestMapping("/simu/query/records.htm")
    public void query(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 1.获取登录信息
        TradeSession loginInfo = this.getCustSession();
        // 2.获取查询条件
        QueryRecordsCmd queryRecordsCmd = getCommand(QueryRecordsCmd.class);
        // 3.获取用户授权状态
        AcctDataAuthDto acctDataAuthInfo = accCenterService.getAcctDataAuthInfo(loginInfo.getUser().getHboneNo());
        // 4.查询交易记录
        QueryDealOrderListResponse queryResp = queryRecords(queryRecordsCmd, loginInfo, acctDataAuthInfo);
        // 5.构建返回信息实体
        QueryRecordsDto rst = buildTradeRecodesRst(queryResp, acctDataAuthInfo.getIsDataAuth());

        if (CgiParamUtil.isH5() && CgiParamUtil.isEncParam()) {
            if (rst.getTradeRecordList() != null && rst.getTradeRecordList().size() > 0) {
                for (TradeFlowRspDTO dto : rst.getTradeRecordList()) {
                    dto.setBankAcct(getEncParamters(dto.getBankAcct()));
                }
            }
        }

        write(rst, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }


    /**
     * @api {GET} /simu/query/recordsV2.htm records
     * @apiVersion 1.0.0
     * @apiGroup QueryHighTradeRecordsController
     * @apiName records
     * @apiDescription 查询交易记录列表
     * @apiParam (请求参数) {String} transType
     * @apiParam (请求参数) {String} transStatus
     * @apiParam (请求参数) {String} beginDate
     * @apiParam (请求参数) {String} endDate
     * @apiParam (请求参数) {String} fundCodeOrNameOrAbbr
     * @apiParam (请求参数) {String} cpAcctNo
     * @apiParam (请求参数) {String} targetPage
     * @apiParam (请求参数) {String} pageSize
     * @apiParam (请求参数) {String} tokenId 客户端token
     * @apiParam (请求参数) {String} corpId 商户号
     * @apiParam (请求参数) {String} actionId 活动号
     * @apiParam (请求参数) {String} operIp IP
     * @apiParam (请求参数) {String} disCode 分销代码
     * @apiParam (请求参数) {String} outletCode 分销网点号
     * @apiParamExample 请求参数示例
     * tokenId=weue&corpId=c&endDate=LRfAHsHeY&pageSize=NJ&disCode=kTmsgc&beginDate=t&transType=sMqeKVbdVl&operIp=s&actionId=Hc&cpAcctNo=bJPoa&transStatus=RwWryxU&fundCodeOrNameOrAbbr=aC&outletCode=c&targetPage=LLlEwh
     * @apiSuccess (响应结果) {Array} tradeRecordList
     * @apiSuccess (响应结果) {String} tradeRecordList.appSheetSerialNo 订单号
     * @apiSuccess (响应结果) {String} tradeRecordList.fundCode 产品编码
     * @apiSuccess (响应结果) {String} tradeRecordList.fundNameAbbr 产品名
     * @apiSuccess (响应结果) {String} tradeRecordList.time 交易时间
     * @apiSuccess (响应结果) {String} tradeRecordList.tradeFlag 业务编码
     * @apiSuccess (响应结果) {String} tradeRecordList.tradeStatus 交易状态
     * @apiSuccess (响应结果) {String} tradeRecordList.payStatus 支付状态
     * @apiSuccess (响应结果) {String} tradeRecordList.payChannelCode 支付方式
     * @apiSuccess (响应结果) {String} tradeRecordList.finaDirect 赎回去向
     * @apiSuccess (响应结果) {String} tradeRecordList.transType
     * @apiSuccess (响应结果) {Number} tradeRecordList.vol 申请份额
     * @apiSuccess (响应结果) {Number} tradeRecordList.confirmVol 确认份额
     * @apiSuccess (响应结果) {Number} tradeRecordList.amount 申请金额
     * @apiSuccess (响应结果) {Number} tradeRecordList.confirmAmount 确认金额
     * @apiSuccess (响应结果) {Number} tradeRecordList.discountfee 手续费
     * @apiSuccess (响应结果) {String} tradeRecordList.bankCode 银行编码
     * @apiSuccess (响应结果) {String} tradeRecordList.bankAcct 银行卡号
     * @apiSuccess (响应结果) {String} tradeRecordList.divMode 分红方式
     * @apiSuccess (响应结果) {String} tradeRecordList.isMutiCardTradeFlag 是否多卡支付
     * @apiSuccess (响应结果) {Array} tradeRecordList.mutiCardList 多银行卡信息
     * @apiSuccess (响应结果) {String} tradeRecordList.mutiCardList.bankCode 银行编码
     * @apiSuccess (响应结果) {String} tradeRecordList.mutiCardList.bankName 银行名称
     * @apiSuccess (响应结果) {String} tradeRecordList.mutiCardList.bankAcct 银行账号
     * @apiSuccess (响应结果) {Number} tradeRecordList.mutiCardList.appAmt 申请金额
     * @apiSuccess (响应结果) {String} tradeRecordList.isVolTansfer
     * @apiSuccess (响应结果) {String} tradeRecordList.mergeSubmitFlag 合并上报标识 1-合并上报
     * @apiSuccess (响应结果) {String} tradeRecordList.isAuth 是否授权  '1',是  '0'：否
     * @apiSuccess (响应结果) {String} tradeRecordList.hasHZHKProduct 持有好臻，香港产品  '1',是  '0'：否
     * @apiSuccess (响应结果) {String} tradeRecordList.currency 币种
     * @apiSuccess (响应结果) {String} tradeRecordList.isHz 是否好臻 1-是
     * @apiSuccess (响应结果) {String} tradeRecordList.continuanceFlag 顺延标识，0-否、1-是
     * @apiSuccess (响应结果) {String} tradeRecordList.stageFlag 拆单标识（淡水泉分期成立），0-否、1-是
     * @apiSuccess (响应结果) {String} hasHZProduct 是否持有好臻产品 0:没有,1:有
     * @apiSuccess (响应结果) {String} hasHKProduct 是否持有好买香港产品  0:没有,1:有
     * @apiSuccess (响应结果) {String} isAuth 是否授权  0:没有,1:有授权
     * @apiSuccess (响应结果) {Number} totalCount
     * @apiSuccess (响应结果) {Number} totalPage
     * @apiSuccess (响应结果) {Number} currentPage
     * @apiSuccessExample 响应结果示例
     * {"isAuth":"60zJaM","tradeRecordList":[{"confirmAmount":740.*************,"confirmVol":1538.************,"mergeSubmitFlag":"kuJSgUY","bankAcct":"qDdtZC","continuanceFlag":"KQQ2a","appSheetSerialNo":"ApMSgZs","mutiCardList":[{"bankCode":"bBsOjhD","appAmt":3252.***********,"bankAcct":"dDz0712n","bankName":"DCUZ"}],"vol":6881.************,"isAuth":"FEpaSN3bW9","fundCode":"OPa","payChannelCode":"Ie4EGs2KMa","currency":"99igAM","isVolTansfer":"SA8YS","fundNameAbbr":"OGPfc","bankCode":"oqfj9YB8W","amount":4540.************,"discountfee":7686.************,"isMutiCardTradeFlag":"j","finaDirect":"eABR","tradeFlag":"g8d6n","divMode":"E6zX2Ri4jp","isHz":"Xib","hasHZHKProduct":"slHiaiTU","transType":"qH2n","tradeStatus":"A","time":"A3NxmCQ5","stageFlag":"MqoIB85","payStatus":"njN"}],"hasHZProduct":"zrgCskt0","totalPage":7089,"hasHKProduct":"PRqzkbWn","totalCount":8574,"currentPage":5702}
     */
    @RequestMapping("/simu/query/recordsV2.htm")
    public void records(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 1.获取登录信息
        TradeSession loginInfo = this.getCustSession();
        // 2.获取查询条件
        QueryRecordsCmd queryRecordsCmd = getCommand(QueryRecordsCmd.class);
        // 3.获取用户授权状态
        AcctDataAuthDto acctDataAuthInfo = accCenterService.getAcctDataAuthInfo(loginInfo.getUser().getHboneNo());
        // 4.查询交易记录
        QueryDealOrderListResponse queryResp = queryRecords(queryRecordsCmd, loginInfo, acctDataAuthInfo);
        // 5.构建返回信息实体
        QueryRecordsDto rst = buildTradeRecodesRstV2(queryResp, acctDataAuthInfo.getIsDataAuth());

        if (CgiParamUtil.isH5() && CgiParamUtil.isEncParam()) {
            if (rst.getTradeRecordList() != null && rst.getTradeRecordList().size() > 0) {
                for (TradeFlowRspDTO dto : rst.getTradeRecordList()) {
                    dto.setBankAcct(getEncParamters(dto.getBankAcct()));
                }
            }
        }

        write(rst, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * @param queryRecordsCmd
     * @param loginInfo
     * @return com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListResponse
     * @Description 查询交易记录
     * <AUTHOR>
     * @Date 2018/11/27 17:11
     **/
    private QueryDealOrderListResponse queryRecords(QueryRecordsCmd queryRecordsCmd, TradeSession loginInfo, AcctDataAuthDto acctDataAuthInfo) {
        QueryDealOrderListRequest queryListRequest = buildQueryDealOrderListCondition(loginInfo.getUser(), queryRecordsCmd, acctDataAuthInfo);

        return queryDealOrderListFacade.execute(queryListRequest);
    }

    private QueryDealOrderListRequest buildQueryDealOrderListCondition(UserInfo userInfo, QueryRecordsCmd queryRecordsCmd, AcctDataAuthDto acctDataAuthInfo) {
        QueryDealOrderListRequest queryRequest = new QueryDealOrderListRequest();
        // 查询好买分销列表
        List<String> disCodeList = getDisCodeList();
        queryRequest.setDisCodeList(disCodeList);
        // 交易账号
        queryRequest.setTxAcctNo(userInfo.getTxAcctNo());
        // 一账通号
        queryRequest.setHbOneNo(userInfo.getHboneNo());
        // 目标页
        queryRequest.setPageNo(getIntFromString(queryRecordsCmd.getTargetPage(), START_PAGE_NUM));
        // 每页记录数
        queryRequest.setPageSize(getIntFromString(queryRecordsCmd.getPageSize(), DEFAULT_PAGE_SIZE));
        // 查询起始时间
        queryRequest.setAppBeginDtm(DateUtils.formatToDate(queryRecordsCmd.getBeginDate(), DateUtils.YYYYMMDD));
        // 查询结束时间
        queryRequest.setAppEndDtm(DateUtils.formatToDate(queryRecordsCmd.getEndDate(), DateUtils.YYYYMMDD));
        // 订单状态
        queryRequest.setOrderStatusArr(transferTransStatus(queryRecordsCmd.getTransStatus()));
        // 产品代码或简称
        queryRequest.setFundNameOrCode(decodeFundCodeOrNameOrAbbr(queryRecordsCmd.getFundCodeOrNameOrAbbr()));
        // 资金账号
        queryRequest.setCpAcctNo(queryRecordsCmd.getCpAcctNo());
        // 业务类型
        queryRequest.setmBusiCodeArr(convertMBusicoeArr(queryRecordsCmd.getTransType(), BUSI_CODE_SPLIT));
        // 是否授权
        if (YesOrNoEnum.YES.getCode().equals(acctDataAuthInfo.getIsDataAuth())) {
            queryRequest.setNotFilterHkFund(YesOrNoEnum.YES.getCode());
            queryRequest.setNotFilterHzFund(YesOrNoEnum.YES.getCode());
            if (YesOrNoEnum.YES.getCode().equals(acctDataAuthInfo.getIsHkDataQuarantine())) {
                queryRequest.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            }
        } else {
            queryRequest.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            queryRequest.setNotFilterHzFund(YesOrNoEnum.NO.getCode());
        }
        return queryRequest;
    }

    private List<String> getDisCodeList() {
        QueryHbDisCodeListRequest queryHbDisCodeListRequest = new QueryHbDisCodeListRequest();
        Result<List<DisDto>> listResult = queryHbDisCodeListFacade.execute(queryHbDisCodeListRequest);
        List<DisDto> dtoList = listResult.getData();
        List<String> disCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dtoList)) {
            for (DisDto disDto : dtoList) {
                if (YesOrNoEnum.YES.getCode().equals(disDto.getHowbuyFlag())) {
                    disCodeList.add(disDto.getDisCode());
                }
            }
        } else {
            disCodeList.add(DisCodeEnum.HM.getCode());
        }
        return disCodeList;
    }

    private int getIntFromString(String digit, int defautValue) {
        try {
            return Integer.parseInt(digit);
        } catch (Exception e) {
            log.error("getIntFromString|error{}", e.getMessage(), e);
        }
        return defautValue;
    }

    private QueryRecordsDto buildTradeRecodesRstV2(QueryDealOrderListResponse queryResp, String isAuth) {
        QueryRecordsDto queryRecordsDto = new QueryRecordsDto();
        // 分页信息
        queryRecordsDto.setCurrentPage(queryResp.getPageNo());
        queryRecordsDto.setTotalCount(queryResp.getTotalCount());
        queryRecordsDto.setTotalPage(queryResp.getTotalPage());
        queryRecordsDto.setHasHKProduct(queryResp.getHasHKProduct());
        queryRecordsDto.setHasHZProduct(queryResp.getHasHZProduct());
        queryRecordsDto.setIsAuth(isAuth);

        List<DealOrderBean> dealOrders = queryResp.getDealOrderList();
        if (CollectionUtils.isEmpty(dealOrders)) {
            queryRecordsDto.setTradeRecordList(new ArrayList<>());
            return queryRecordsDto;
        }
        List<String> fundCodeList = dealOrders.stream().map(DealOrderBean::getProductCode).distinct().collect(Collectors.toList());
        Map<String, Integer> volPrecisionMap = dtmsService.getVolPrecisionByFundCode(fundCodeList);
        List<TradeFlowRspDTO> tradeRecodes = new ArrayList<>();
        for (DealOrderBean dealOrder : dealOrders) {
            TradeFlowRspDTO tradeFlowRspDTO = getTradeFlowRspDTO(queryResp, isAuth, volPrecisionMap, dealOrder);
            tradeRecodes.add(tradeFlowRspDTO);
        }
        queryRecordsDto.setTradeRecordList(tradeRecodes);
        return queryRecordsDto;

    }

    /**
     * 交易记录
     */
    private TradeFlowRspDTO getTradeFlowRspDTO(QueryDealOrderListResponse queryResp, String isAuth, Map<String, Integer> volPrecisionMap, DealOrderBean dealOrder) {
        TradeFlowRspDTO  tradeFlowRspDTO = new TradeFlowRspDTO();
        // 产品代码
        tradeFlowRspDTO.setFundCode(dealOrder.getProductCode());
        // 产品名称
        tradeFlowRspDTO.setFundNameAbbr(dealOrder.getProductName());
        // 订单号
        tradeFlowRspDTO.setAppSheetSerialNo(dealOrder.getDealNo());
        // 申请金额
        tradeFlowRspDTO.setAmount(dealOrder.getAppAmt());
        // 确认金额
        tradeFlowRspDTO.setConfirmAmount(dealOrder.getAckAmt());
        tradeFlowRspDTO.setTransType("5");
        // 支付方式
        tradeFlowRspDTO.setPayChannelCode(dealOrder.getPaymentType());
        // 赎回去向
        tradeFlowRspDTO.setFinaDirect(dealOrder.getRedeemDirection());
        // 订单状态
        tradeFlowRspDTO.setTradeStatus(dealOrder.getOrderStatus());
        // 付款状态
        tradeFlowRspDTO.setPayStatus(dealOrder.getPayStatus());
        //获取交易类型:对应mBusiCode
        tradeFlowRspDTO.setTradeFlag(getTradeFlagV2(dealOrder));
        // 下单时间
        tradeFlowRspDTO.setTime(DateUtils.formatToString(dealOrder.getAppDtm(), DateUtils.YYYYMMDD));
        // 份额精度
        int precision = Optional.ofNullable(volPrecisionMap.get(dealOrder.getProductCode())).orElse(2);
        // 申请份额
        tradeFlowRspDTO.setVol(MoneyUtil.formatMoney(dealOrder.getAppVol(), precision));
        // 申请份额
        if (dealOrder.getAppVol() != null) {
            tradeFlowRspDTO.setVolStr(MoneyUtil.formatMoney(dealOrder.getAppVol(), precision) + "");
        }
        // 确认份额
        tradeFlowRspDTO.setConfirmVol(MoneyUtil.formatMoney(dealOrder.getAckVol(), precision));
        if (dealOrder.getAckVol() != null) {
            tradeFlowRspDTO.setConfirmVolStr(MoneyUtil.formatMoney(dealOrder.getAckVol(), precision) + "");
        }
        // 手续费
        tradeFlowRspDTO.setDiscountfee(dealOrder.getFee());
        // 银行卡号
        tradeFlowRspDTO.setBankAcct(dealOrder.getBankAcct());
        // 银行代码
        tradeFlowRspDTO.setBankCode(dealOrder.getBankCode());
        // 分红方式
        tradeFlowRspDTO.setDivMode(dealOrder.getDivMode());
        // 合并上报标识
        tradeFlowRspDTO.setMergeSubmitFlag(dealOrder.getMergeSubmitFlag());
        // 币种
        tradeFlowRspDTO.setCurrency(dealOrder.getCurrency());
        // 巨额标识
        tradeFlowRspDTO.setContinuanceFlag(dealOrder.getContinuanceFlag());
        // 多卡交易记录特殊处理
        List<MutiBankCardInfo> mutiBankCardList = getMutiCardList(dealOrder.getMemo());
        // 多卡支付交易银行卡信息
        tradeFlowRspDTO.setMutiCardList(mutiBankCardList);
        // 是否多卡支付交易
        tradeFlowRspDTO.setIsMutiCardTradeFlag(mutiCardFlag(mutiBankCardList));
        tradeFlowRspDTO.setIsAuth(isAuth);
        if (YesOrNoEnum.YES.getCode().equals(queryResp.getHasHKProduct()) && YesOrNoEnum.YES.getCode().equals(queryResp.getHasHZProduct())) {
            tradeFlowRspDTO.setHasHZHKProduct(YesOrNoEnum.YES.getCode());
        } else {
            tradeFlowRspDTO.setHasHZHKProduct(YesOrNoEnum.NO.getCode());
        }
        // 设置好臻标志
        if (HZ_DISCODE.equals(dealOrder.getDisCode())) {
            tradeFlowRspDTO.setIsHz(YesOrNoEnum.YES.getCode());
        } else {
            tradeFlowRspDTO.setIsHz(YesOrNoEnum.NO.getCode());
        }
        return tradeFlowRspDTO;
    }


    private QueryRecordsDto buildTradeRecodesRst(QueryDealOrderListResponse queryResp, String isAuth) {
        QueryRecordsDto queryRecordsDto = new QueryRecordsDto();
        // 分页信息
        queryRecordsDto.setCurrentPage(queryResp.getPageNo());
        queryRecordsDto.setTotalCount(queryResp.getTotalCount());
        queryRecordsDto.setTotalPage(queryResp.getTotalPage());
        queryRecordsDto.setHasHKProduct(queryResp.getHasHKProduct());
        queryRecordsDto.setHasHZProduct(queryResp.getHasHZProduct());
        queryRecordsDto.setIsAuth(isAuth);

        List<DealOrderBean> dealOrders = queryResp.getDealOrderList();
        if (CollectionUtils.isEmpty(dealOrders)) {
            queryRecordsDto.setTradeRecordList(new ArrayList<>());
            return queryRecordsDto;
        }
        List<TradeFlowRspDTO> tradeRecodes = new ArrayList<>();
        TradeFlowRspDTO tradeFlowRspDTO = null;
        for (DealOrderBean dealOrder : dealOrders) {
            tradeFlowRspDTO = new TradeFlowRspDTO();
            // 产品代码
            tradeFlowRspDTO.setFundCode(dealOrder.getProductCode());
            // 产品名称
            tradeFlowRspDTO.setFundNameAbbr(dealOrder.getProductName());
            // 订单号
            tradeFlowRspDTO.setAppSheetSerialNo(dealOrder.getDealNo());
            // 申请金额
            tradeFlowRspDTO.setAmount(dealOrder.getAppAmt());
            // 确认金额
            tradeFlowRspDTO.setConfirmAmount(dealOrder.getAckAmt());
            tradeFlowRspDTO.setTransType("5");
            // 支付方式
            tradeFlowRspDTO.setPayChannelCode(dealOrder.getPaymentType());
            // 赎回去向
            tradeFlowRspDTO.setFinaDirect(dealOrder.getRedeemDirection());
            // 订单状态
            tradeFlowRspDTO.setTradeStatus(dealOrder.getOrderStatus());
            // 付款状态
            tradeFlowRspDTO.setPayStatus(dealOrder.getPayStatus());
            //获取交易类型:对应mBusiCode
            tradeFlowRspDTO.setTradeFlag(getTradeFlag(dealOrder));
            // 下单时间
            tradeFlowRspDTO.setTime(DateUtils.formatToString(dealOrder.getAppDtm(), DateUtils.YYYYMMDD));
            // 申请份额
            tradeFlowRspDTO.setVol(dealOrder.getAppVol() == null ? null : dealOrder.getAppVol().abs());
            // 确认份额
            tradeFlowRspDTO.setConfirmVol(dealOrder.getAckVol() == null ? null : dealOrder.getAckVol().abs());
            // 手续费
            tradeFlowRspDTO.setDiscountfee(dealOrder.getFee());
            // 银行卡号
            tradeFlowRspDTO.setBankAcct(dealOrder.getBankAcct());
            // 银行代码
            tradeFlowRspDTO.setBankCode(dealOrder.getBankCode());
            // 分红方式
            tradeFlowRspDTO.setDivMode(dealOrder.getDivMode());
            // 合并上报标识
            tradeFlowRspDTO.setMergeSubmitFlag(dealOrder.getMergeSubmitFlag());
            // 币种
            tradeFlowRspDTO.setCurrency(dealOrder.getCurrency());
            // 巨额标识
            tradeFlowRspDTO.setContinuanceFlag(dealOrder.getContinuanceFlag());
            // 多卡交易记录特殊处理
            List<MutiBankCardInfo> mutiBankCardList = getMutiCardList(dealOrder.getMemo());
            // 多卡支付交易银行卡信息
            tradeFlowRspDTO.setMutiCardList(mutiBankCardList);
            // 是否多卡支付交易
            tradeFlowRspDTO.setIsMutiCardTradeFlag(mutiCardFlag(mutiBankCardList));
            tradeFlowRspDTO.setIsAuth(isAuth);
            if (YesOrNoEnum.YES.getCode().equals(queryResp.getHasHKProduct()) && YesOrNoEnum.YES.getCode().equals(queryResp.getHasHZProduct())) {
                tradeFlowRspDTO.setHasHZHKProduct(YesOrNoEnum.YES.getCode());
            } else {
                tradeFlowRspDTO.setHasHZHKProduct(YesOrNoEnum.NO.getCode());
            }
            // 设置好臻标志
            if (HZ_DISCODE.equals(dealOrder.getDisCode())) {
                tradeFlowRspDTO.setIsHz(YesOrNoEnum.YES.getCode());
            } else {
                tradeFlowRspDTO.setIsHz(YesOrNoEnum.NO.getCode());
            }
            tradeRecodes.add(tradeFlowRspDTO);
            // 部分订单需要拆单
            splitAndResetDealOrder(dealOrder, tradeRecodes);
        }
        queryRecordsDto.setTradeRecordList(tradeRecodes);

        return queryRecordsDto;

    }

    /**
     * 部分订单需要拆分与转换
     *
     * @param tradeRecodes 订单列表
     * @return 转后的订单列表
     * 基金转换：高端中台拆
     * 基金转换出，转义成赎回：基金代码、基金简称、申请份额、净值日期、确认净值、确认金额、确认份额
     * 基金转换入，转义成申购：基金代码=目标基金代码、基金简称=目标基金简称、申请份额、净值日期=目标基金净值日期、确认净值=目标基金确认净值、确认金额=目标基金确认金额、确认份额=目标基金确认份额
     */
    private void splitAndResetDealOrder(DealOrderBean dealOrderBean, List<TradeFlowRspDTO> tradeRecodes) {
        if (BusinessCodeEnum.FUND_EXCHANGE.getMCode().equals(dealOrderBean.getmBusiCode())) {
            TradeFlowRspDTO tradeFlowRspDTO = new TradeFlowRspDTO();
            BeanUtils.copyProperties(dealOrderBean, tradeFlowRspDTO);
            tradeFlowRspDTO.setTradeFlag(BusinessCodeEnum.PURCHASE.getMCode());
            tradeFlowRspDTO.setFundCode(dealOrderBean.getTransferInFundCode());
            tradeFlowRspDTO.setFundNameAbbr(dealOrderBean.getTransferInFundName());
            tradeFlowRspDTO.setConfirmVol(dealOrderBean.getTransferInAckVol());
            tradeFlowRspDTO.setConfirmAmount(dealOrderBean.getTransferInAckAmt());
            tradeRecodes.add(tradeFlowRspDTO);
        }
    }


    /**
     * 转化交易状态
     *
     * @param transStatus
     * @return
     */
    private String[] transferTransStatus(String transStatus) {
        String[] returnString = null;
        if ("1".equals(transStatus)) {
            returnString = new String[]{"1"};
        } else if ("2".equals(transStatus)) {
            returnString = new String[]{"5", "6"};
        } else if ("3".equals(transStatus)) {
            returnString = new String[]{"2", "3"};
        } else if ("4".equals(transStatus)) {
            returnString = new String[]{"4"};
        }
        return returnString;
    }

    /**
     * getTradeType:(获取交易标识：对应mBusiCode)
     *
     * @param highOrderBean
     * @return
     * <AUTHOR>
     * @date 2018年1月15日 下午9:34:39
     */
    private String getTradeFlagV2(DealOrderBean highOrderBean) {

        String productType = highOrderBean.getProductType();
        if (ProductTypeEnum.SM.getCode().equals(highOrderBean.getProductType())) {
            productType = highOrderBean.getProductSubType();
        }

        if (ScaleTypeEnum.CONSIGNMENT.getCode().equals(highOrderBean.getScaleType()) && ProductTypeEnum.PE_VC.getCode().equals(productType)
                && BusinessCodeEnum.DIV.getMCode().equals(highOrderBean.getmBusiCode()) && BigDecimal.ZERO.compareTo(highOrderBean.getAckAmt()) < 0) {
            // 股权回款:股权类产品，现金分红，交易类型转换为1999
            return "1999";
        } else {
            return highOrderBean.getmBusiCode();
        }
    }

    /**
     * 获取业务类型
     * 基金转换：高端中台拆
     * 基金转换出，转义成赎回：基金代码、基金简称、申请份额、净值日期、确认净值、确认金额、确认份额
     * 基金转换入，转义成申购：基金代码=目标基金代码、基金简称=目标基金简称、申请份额、净值日期=目标基金净值日期、确认净值=目标基金确认净值、确认金额=目标基金确认金额、确认份额=目标基金确认份额
     * 系列合并出：转换成卖出
     * 系列合并入：转换成买入
     * 平衡因子兑换：
     * 兑换的份额为负，转换成强减
     * 兑换的份额为正，转换为强增
     */
    private String getTradeFlag(DealOrderBean highOrderBean) {
        String productType = highOrderBean.getProductType();
        if (ProductTypeEnum.SM.getCode().equals(highOrderBean.getProductType())) {
            productType = highOrderBean.getProductSubType();
        }
        if (ScaleTypeEnum.CONSIGNMENT.getCode().equals(highOrderBean.getScaleType()) && ProductTypeEnum.PE_VC.getCode().equals(productType)
                && BusinessCodeEnum.DIV.getMCode().equals(highOrderBean.getmBusiCode()) && BigDecimal.ZERO.compareTo(highOrderBean.getAckAmt()) < 0) {
            // 股权回款:股权类产品，现金分红，交易类型转换为1999
            return "1999";
        } else {
            // 基金转换的转为赎回,因为转入的部分后面会被拆单成申购,所以订单主题部分默认转为转出,即赎回
            if (BusinessCodeEnum.FUND_EXCHANGE.getMCode().equals(highOrderBean.getmBusiCode())) {
                return BusinessCodeEnum.REDEEM.getMCode();
            }
            // 系列合并出：转换成卖出
            if (BusinessCodeEnum.SERIES_MERGE_OUT.getMCode().equals(highOrderBean.getmBusiCode())) {
                return BusinessCodeEnum.REDEEM.getMCode();
            }
            // 系列合并入：转换成买入
            if (BusinessCodeEnum.SERIES_MERGE_IN.getMCode().equals(highOrderBean.getmBusiCode())) {
                return BusinessCodeEnum.PURCHASE.getMCode();
            }
            //  平衡因子兑换：兑换的份额为负，转换成强减,兑换的份额为正，转换为强增
            if (BusinessCodeEnum.BALANCE_FACTOR_EXCHANGE.getMCode().equals(highOrderBean.getmBusiCode())) {
                if (highOrderBean.getAppVol() != null && BigDecimal.ZERO.compareTo(highOrderBean.getAppVol()) > 0) {
                    return BusinessCodeEnum.FORCE_SUBTRACT.getMCode();
                }
                if (highOrderBean.getAppVol() != null && BigDecimal.ZERO.compareTo(highOrderBean.getAppVol()) <= 0) {
                    return BusinessCodeEnum.FORCE_ADD.getMCode();
                }

            }
            return highOrderBean.getmBusiCode();
        }
    }

    private String decodeFundCodeOrNameOrAbbr(String fundCodeOrNameOrAbbr) {
        if (StringUtils.isEmpty(fundCodeOrNameOrAbbr)) {
            return null;
        }
        try {
            return java.net.URLDecoder.decode(fundCodeOrNameOrAbbr, "utf-8");
        } catch (Exception e) {
            log.error("decodeFundName|fundCodeOrNameOrAbbr:{} error{}", fundCodeOrNameOrAbbr, e.getMessage(), e);
        }
        return null;
    }

    private String[] convertMBusicoeArr(String transType, String splitChar) {
        if (StringUtils.isEmpty(transType)) {
            return null;
        }

        return transType.split(splitChar);
    }
}
