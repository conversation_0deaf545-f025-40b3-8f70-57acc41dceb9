package com.howbuy.cgi.trade.simu.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.CommonAnnotationBeanPostProcessor;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/5 15:46
 * @since JDK 1.8
 */

@Configuration
public class DubboConfig {
    @Bean
    public CommonAnnotationBeanPostProcessor commonAnnotationBeanPostProcessor() {
        return new CommonAnnotationBeanPostProcessor();
    }
//    @Bean(name = "commonThreadPool")
//    public NewCommonThreadPool newCommonThreadPool() {
//        return new NewCommonThreadPool();
//    }


}
