/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.model.dto.relatedaccount;

import java.io.Serializable;

/**
 * 请在此添加描述
 *
 * <AUTHOR>
 * @date 2021/7/16 13:35
 * @since JDK 1.8
 */
public class InviteDtlDto implements Serializable {
    private static final long serialVersionUID = 2468686956764454871L;
    /**
     * 邀请id
     */
    private String inviteId;
    /**
     * 角色 0-本人;1-父亲;2-母亲;3-爱人;4-爱人父;5-爱人母;6-儿子;7-女儿
     */
    private String role;
    /**
     * 被邀请人手机
     */
    private String mobile;
    /**
     * 到期时间yyyyMMddHHmmss
     */
    private String timeLimit;

    public String getInviteId() {
        return inviteId;
    }

    public void setInviteId(String inviteId) {
        this.inviteId = inviteId;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getTimeLimit() {
        return timeLimit;
    }

    public void setTimeLimit(String timeLimit) {
        this.timeLimit = timeLimit;
    }
}