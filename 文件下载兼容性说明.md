# 系统异常监控 Filter 文件下载兼容性说明

## 问题背景

在实现系统异常监控 Filter 时，需要特别考虑文件下载场景的兼容性。文件下载请求具有以下特点：

1. **返回二进制流**：不是JSON格式的响应
2. **大文件传输**：可能包含大量数据
3. **特殊Content-Type**：如 `application/octet-stream`、`application/pdf` 等
4. **性能敏感**：不应影响文件下载的性能

## 兼容性解决方案

### 1. 多层过滤机制

#### 第一层：请求路径过滤
在 `shouldMonitor()` 方法中，通过请求URI识别文件下载请求：

```java
private boolean isFileDownloadRequest(String requestURI) {
    return requestURI.contains("/filedownload.htm") ||
           requestURI.contains("/contracttemplatedownload.htm") ||
           requestURI.endsWith(".pdf") ||
           requestURI.endsWith(".doc") ||
           requestURI.endsWith(".docx") ||
           requestURI.endsWith(".xls") ||
           requestURI.endsWith(".xlsx");
}
```

**覆盖的文件下载场景**：
- `/simu/query/filedownload.htm` - 通用文件下载
- `/simu/query/contracttemplatedownload.htm` - 合同模板下载
- 直接文件访问（通过文件扩展名识别）

#### 第二层：Content-Type检测
在 `checkResponseForSystemError()` 方法中检查响应的Content-Type：

```java
private boolean isFileStreamResponse(String contentType) {
    if (contentType == null) return false;
    
    String lowerContentType = contentType.toLowerCase();
    return lowerContentType.contains("application/octet-stream") ||
           lowerContentType.contains("application/pdf") ||
           lowerContentType.contains("application/msword") ||
           lowerContentType.contains("application/vnd.ms-excel") ||
           lowerContentType.contains("application/vnd.openxmlformats") ||
           lowerContentType.contains("image/") ||
           lowerContentType.contains("video/") ||
           lowerContentType.contains("audio/");
}
```

#### 第三层：二进制数据检测
通过分析响应体内容判断是否为二进制数据：

```java
private boolean isBinaryData(String responseBody) {
    if (responseBody == null || responseBody.length() < 10) {
        return false;
    }
    
    // 检查前100个字符中非可打印字符的比例
    int nonPrintableCount = 0;
    int sampleSize = Math.min(100, responseBody.length());
    
    for (int i = 0; i < sampleSize; i++) {
        char c = responseBody.charAt(i);
        if (c < 32 && c != '\n' && c != '\r' && c != '\t') {
            nonPrintableCount++;
        }
    }
    
    // 如果非可打印字符超过20%，认为是二进制数据
    return (nonPrintableCount * 100.0 / sampleSize) > 20;
}
```

### 2. ResponseWrapper 优化

#### 处理大文件
为了避免大文件占用过多内存，ResponseWrapper 只读取前1000字节用于检测：

```java
public String getResponseBody() {
    writer.flush();
    try {
        byte[] data = outputStream.toByteArray();
        if (data.length > 1000) {
            // 对于大文件，只检查前1000字节
            return new String(data, 0, 1000, "UTF-8");
        }
        return outputStream.toString("UTF-8");
    } catch (Exception e) {
        LOG.error("获取响应体内容失败", e);
        return "";
    }
}
```

#### 支持多种写入方式
增强 ServletOutputStream 以支持文件下载的各种写入方式：

```java
@Override
public javax.servlet.ServletOutputStream getOutputStream() throws IOException {
    return new javax.servlet.ServletOutputStream() {
        @Override
        public void write(int b) throws IOException {
            outputStream.write(b);
        }
        
        @Override
        public void write(byte[] b) throws IOException {
            outputStream.write(b);
        }
        
        @Override
        public void write(byte[] b, int off, int len) throws IOException {
            outputStream.write(b, off, len);
        }
        // ...
    };
}
```

## 实际应用场景

### 1. FileDownloadController
```java
@RequestMapping("/simu/query/filedownload.htm")
public void handleRequestInternal(HttpServletRequest request, HttpServletResponse response) {
    // ...
    if (fileSdkPathInfo != null && FileSdkUtil.exists(fileSdkPathInfo)) {
        FileDownUtil.doDownloadV3(fileSdkPathInfo, response);  // 直接写入文件流
    }
    // ...
}
```

**Filter处理**：
- 通过URI识别为文件下载请求
- 自动跳过JSON监控
- 正常传递文件流到客户端

### 2. ContractTemplateDownloadController
```java
@RequestMapping("/simu/query/contracttemplatedownload.htm")
public void filedownloadInProcess(HttpServletRequest request, HttpServletResponse response) {
    // ...
    if (FileSdkUtil.exists(fileSdkPathInfo)) {
        FileDownUtil.doDownloadV3(fileSdkPathInfo, response);  // 文件下载
    }
    // ...
}
```

**Filter处理**：
- URI匹配文件下载模式
- Content-Type为 `application/octet-stream` 或 `application/pdf`
- 跳过监控，保证下载性能

### 3. 错误页面下载
```java
private void showDownloadError(String errorMsg, HttpServletResponse response) {
    // 返回错误信息，但仍然是文件下载上下文
}
```

**Filter处理**：
- 虽然返回错误信息，但在文件下载上下文中
- 通过URI识别跳过监控
- 避免误报

## 性能影响分析

### 1. 内存使用
- **优化前**：可能缓存整个文件内容
- **优化后**：最多只缓存1000字节用于检测

### 2. CPU开销
- **路径检查**：O(1) 字符串匹配，开销极小
- **Content-Type检查**：O(1) 字符串包含检查
- **二进制检测**：最多检查100个字符，开销可控

### 3. 网络传输
- 文件流直接传输，无额外开销
- 不影响下载速度和用户体验

## 测试覆盖

### 1. 单元测试
- `testFilterWithFileDownloadRequest()` - 测试文件下载请求跳过监控
- `testFilterWithContractTemplateDownload()` - 测试合同模板下载
- `testFilterWithNonJsonResponse()` - 测试非JSON响应处理

### 2. 集成测试
- 模拟真实文件下载场景
- 验证Content-Type设置
- 确认响应正确传递

## 配置建议

### 1. 监控路径配置
```java
registration.addUrlPatterns("/simu/*");  // 只监控业务接口
```

### 2. 排除模式扩展
如果有新的文件下载接口，可以扩展 `isFileDownloadRequest()` 方法：

```java
private boolean isFileDownloadRequest(String requestURI) {
    return requestURI.contains("/filedownload.htm") ||
           requestURI.contains("/contracttemplatedownload.htm") ||
           requestURI.contains("/newfiledownload.htm") ||  // 新增
           requestURI.endsWith(".pdf") ||
           // ... 其他文件类型
}
```

## 总结

通过多层过滤机制和ResponseWrapper优化，系统异常监控Filter完全兼容文件下载场景：

1. **零影响**：文件下载性能不受影响
2. **智能识别**：自动识别各种文件下载模式
3. **内存安全**：避免大文件导致的内存问题
4. **扩展性强**：易于添加新的文件类型支持

这确保了监控系统既能有效检测系统异常，又不会干扰正常的文件下载功能。
