package com.howbuy.cgi.trade.simu.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.cgi.trade.simu.util.OpsSysMonitor;
import com.howbuy.trace.RequestChainTrace;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统异常监控过滤器
 * 通过检查响应体中的错误码来检测系统异常
 * 
 * 主要用于检测被全局异常处理器处理的系统异常，这些异常不会直接抛出，
 * 而是被转换为错误响应（如 code: "1999"）
 *
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */
public class SystemExceptionMonitorFilter extends OncePerRequestFilter {
    
    private static final Logger LOG = LoggerFactory.getLogger(SystemExceptionMonitorFilter.class);
    
    /**
     * 系统错误码，通常表示非业务异常
     */
    private static final String SYSTEM_ERROR_CODE = "1999";
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        // 只监控 /simu/ 路径下的请求
        if (!shouldMonitor(request)) {
            filterChain.doFilter(request, response);
            return;
        }
        
        // 包装响应以捕获响应体内容
        ResponseWrapper responseWrapper = new ResponseWrapper(response);
        
        try {
            // 继续过滤器链
            filterChain.doFilter(request, responseWrapper);
            
            // 检查响应体中的错误码
            checkResponseForSystemError(request, responseWrapper);
            
        } finally {
            // 将包装的响应内容写回原始响应
            writeResponse(response, responseWrapper);
        }
    }
    
    /**
     * 判断是否需要监控此请求
     */
    private boolean shouldMonitor(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        if (requestURI == null || !requestURI.contains("/simu/")) {
            return false;
        }

        // 排除文件下载相关的接口，这些接口返回文件流而不是JSON
        if (isFileDownloadRequest(requestURI)) {
            return false;
        }

        return true;
    }

    /**
     * 判断是否为文件下载请求
     */
    private boolean isFileDownloadRequest(String requestURI) {
        // 文件下载相关的URI模式
        return requestURI.contains("/filedownload.htm") ||
               requestURI.contains("/contracttemplatedownload.htm") ||
               requestURI.endsWith(".pdf") ||
               requestURI.endsWith(".doc") ||
               requestURI.endsWith(".docx") ||
               requestURI.endsWith(".xls") ||
               requestURI.endsWith(".xlsx");
    }
    
    /**
     * 检查响应体中是否包含系统错误码
     */
    private void checkResponseForSystemError(HttpServletRequest request, ResponseWrapper responseWrapper) {
        try {
            // 检查响应的Content-Type，如果是文件流则跳过
            String contentType = responseWrapper.getContentType();
            if (isFileStreamResponse(contentType)) {
                LOG.debug("跳过文件流响应的监控，Content-Type: {}", contentType);
                return;
            }

            String responseBody = responseWrapper.getResponseBody();

            if (responseBody == null || responseBody.trim().isEmpty()) {
                return;
            }

            // 如果响应体是二进制数据（文件流），跳过检查
            if (isBinaryData(responseBody)) {
                LOG.debug("跳过二进制数据响应的监控");
                return;
            }

            // 尝试解析 JSON 响应
            JSONObject jsonResponse = null;
            try {
                jsonResponse = JSON.parseObject(responseBody);
            } catch (Exception e) {
                // 不是 JSON 格式，跳过检查
                LOG.debug("响应不是JSON格式，跳过监控: {}", e.getMessage());
                return;
            }

            // 检查是否包含系统错误码
            if (jsonResponse != null && SYSTEM_ERROR_CODE.equals(jsonResponse.getString("code"))) {
                // 发送系统异常告警
                sendSystemExceptionAlert(request, jsonResponse, responseBody);
            }

        } catch (Exception e) {
            LOG.error("检查响应体系统错误时发生异常", e);
        }
    }

    /**
     * 判断是否为文件流响应
     */
    private boolean isFileStreamResponse(String contentType) {
        if (contentType == null) {
            return false;
        }

        String lowerContentType = contentType.toLowerCase();
        return lowerContentType.contains("application/octet-stream") ||
               lowerContentType.contains("application/pdf") ||
               lowerContentType.contains("application/msword") ||
               lowerContentType.contains("application/vnd.ms-excel") ||
               lowerContentType.contains("application/vnd.openxmlformats") ||
               lowerContentType.contains("image/") ||
               lowerContentType.contains("video/") ||
               lowerContentType.contains("audio/");
    }

    /**
     * 判断响应体是否为二进制数据
     */
    private boolean isBinaryData(String responseBody) {
        if (responseBody == null || responseBody.length() < 10) {
            return false;
        }

        // 检查是否包含大量非可打印字符（简单的二进制数据检测）
        int nonPrintableCount = 0;
        // 只检查前100个字符
        int sampleSize = Math.min(100, responseBody.length());

        for (int i = 0; i < sampleSize; i++) {
            char c = responseBody.charAt(i);
            if (c < 32 && c != '\n' && c != '\r' && c != '\t') {
                nonPrintableCount++;
            }
        }

        // 如果非可打印字符超过20%，认为是二进制数据
        return (nonPrintableCount * 100.0 / sampleSize) > 20;
    }
    
    /**
     * 发送系统异常告警
     */
    private void sendSystemExceptionAlert(HttpServletRequest request, JSONObject jsonResponse, String responseBody) {
        try {
            // 构建告警信息
            Map<String, Object> alertInfo = buildAlertInfo(request, jsonResponse, responseBody);
            String alertMessage = JSON.toJSONString(alertInfo);
            
            // 使用OpsSysMonitor发送告警
            OpsSysMonitor.warn(alertMessage, OpsSysMonitor.ERROR);
            LOG.error("系统异常告警已发送(通过响应体检测): {}", alertMessage);
            
        } catch (Exception alertEx) {
            // 告警发送失败时记录日志，但不影响主流程
            LOG.error("发送系统异常告警失败", alertEx);
        }
    }
    
    /**
     * 构建告警信息
     */
    private Map<String, Object> buildAlertInfo(HttpServletRequest request, JSONObject jsonResponse, String responseBody) {
        Map<String, Object> alertInfo = new HashMap<>();
        
        // 基本信息
        alertInfo.put("alertType", "SYSTEM_EXCEPTION");
        alertInfo.put("requestUri", request.getRequestURI());
        alertInfo.put("requestMethod", request.getMethod());
        alertInfo.put("traceId", RequestChainTrace.getReqId());
        alertInfo.put("remoteAddr", getClientIpAddress(request));
        
        // 响应信息
        alertInfo.put("responseCode", jsonResponse.getString("code"));
        alertInfo.put("responseDesc", jsonResponse.getString("desc"));
        alertInfo.put("detectionMethod", "RESPONSE_BODY_CHECK");
        
        // 请求参数（敏感信息需要过滤）
        Map<String, String[]> parameterMap = request.getParameterMap();
        if (parameterMap != null && !parameterMap.isEmpty()) {
            Map<String, Object> filteredParams = new HashMap<>();
            for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                String key = entry.getKey();
                String[] values = entry.getValue();
                
                // 过滤敏感参数
                if (isSensitiveParam(key)) {
                    filteredParams.put(key, "***");
                } else if (values != null && values.length > 0) {
                    filteredParams.put(key, values.length == 1 ? values[0] : values);
                }
            }
            alertInfo.put("requestParams", filteredParams);
        }
        
        // 响应体长度信息（避免记录过长的响应体）
        alertInfo.put("responseBodyLength", responseBody.length());
        if (responseBody.length() <= 500) {
            alertInfo.put("responseBody", responseBody);
        } else {
            alertInfo.put("responseBodyPreview", responseBody.substring(0, 500) + "...");
        }
        
        return alertInfo;
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
    
    /**
     * 判断是否为敏感参数
     */
    private boolean isSensitiveParam(String paramName) {
        if (paramName == null) {
            return false;
        }
        
        String lowerParamName = paramName.toLowerCase();
        return lowerParamName.contains("password") || 
               lowerParamName.contains("pwd") ||
               lowerParamName.contains("token") ||
               lowerParamName.contains("secret") ||
               lowerParamName.contains("key") ||
               lowerParamName.contains("idno") ||
               lowerParamName.contains("mobile") ||
               lowerParamName.contains("phone");
    }
    
    /**
     * 将包装的响应内容写回原始响应
     */
    private void writeResponse(HttpServletResponse originalResponse, ResponseWrapper responseWrapper) throws IOException {
        byte[] responseData = responseWrapper.getResponseData();
        if (responseData != null && responseData.length > 0) {
            originalResponse.getOutputStream().write(responseData);
        }
    }
    
    /**
     * 响应包装器，用于捕获响应体内容
     */
    private static class ResponseWrapper extends javax.servlet.http.HttpServletResponseWrapper {
        private final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        private final PrintWriter writer = new PrintWriter(new OutputStreamWriter(outputStream, "UTF-8"));
        
        public ResponseWrapper(HttpServletResponse response) throws UnsupportedEncodingException {
            super(response);
        }
        
        @Override
        public PrintWriter getWriter() throws IOException {
            return writer;
        }
        
        @Override
        public javax.servlet.ServletOutputStream getOutputStream() throws IOException {
            return new javax.servlet.ServletOutputStream() {
                @Override
                public void write(int b) throws IOException {
                    outputStream.write(b);
                }

                @Override
                public void write(byte[] b) throws IOException {
                    outputStream.write(b);
                }

                @Override
                public void write(byte[] b, int off, int len) throws IOException {
                    outputStream.write(b, off, len);
                }

                @Override
                public boolean isReady() {
                    return true;
                }

                @Override
                public void setWriteListener(javax.servlet.WriteListener writeListener) {
                    // Not implemented
                }
            };
        }
        
        public String getResponseBody() {
            writer.flush();
            try {
                // 如果是大文件或二进制数据，只返回前面一小部分用于检测
                byte[] data = outputStream.toByteArray();
                if (data.length > 1000) {
                    // 对于大文件，只检查前1000字节来判断是否为文本
                    return new String(data, 0, 1000, "UTF-8");
                }
                return outputStream.toString("UTF-8");
            } catch (Exception e) {
                LOG.error("获取响应体内容失败", e);
                return "";
            }
        }

        public byte[] getResponseData() {
            writer.flush();
            return outputStream.toByteArray();
        }

        public boolean isLargeResponse() {
            return outputStream.size() > 10240; // 大于10KB认为是大响应
        }
    }
}
