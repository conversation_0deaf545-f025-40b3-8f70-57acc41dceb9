package com.howbuy.cgi.trade.simu.service.task;

import com.howbuy.cgi.trade.simu.model.cmd.QueryBalanceParamCmd;
import com.howbuy.cgi.trade.simu.model.dto.InvitePageDto;
import com.howbuy.cgi.trade.simu.model.vo.SiMuIndexVo;
import com.howbuy.cgi.trade.simu.model.vo.WaitSupSignAgreementFundVo;
import com.howbuy.cgi.trade.simu.service.relatedaccount.InviteService;
import com.howbuy.cgi.trade.simu.service.relatedaccount.RelatedAccountService;
import com.howbuy.kyc.model.KycModel;
import com.howbuy.kyc.service.KycService;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListFacade;
import com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListRequest;
import com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListResponse;
import com.howbuy.trade.account.model.hbone.MemberCustInfoModel;
import com.howbuy.trade.account.service.hbone.HboneService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.stream.Collectors;

/**
 * @Description:设置私募持仓用户信息接口
 * @Author: yun.lu
 * Date: 2023/10/11 17:25
 */
public class SetSiMuAccountInfoTask extends HowbuyBaseTask {
    private static final Logger log = LogManager.getLogger(SetSiMuAccountInfoTask.class);

    private KycService kycService;
    private RelatedAccountService relatedAccountService;
    private QuerySupSignAgreementListFacade querySupSignAgreementListFacade;
    private HboneService hboneService;
    private QueryBalanceParamCmd queryBalanceParamCmd;
    private SiMuIndexVo siMuIndexVo;
    private InviteService inviteService;
    private static final String PRO = "PRO";


    /**
     * 查询查询待补签协议列表
     *
     * @param txAcctNo 交易账号
     */
    public QuerySupSignAgreementListResponse queryQuerySupSignAgreementList(String txAcctNo, String hbOneNo) {
        // 查询待补签协议列表
        QuerySupSignAgreementListRequest req = new QuerySupSignAgreementListRequest();
        req.setTxAcctNo(txAcctNo);
        req.setHbOneNo(hbOneNo);
        return querySupSignAgreementListFacade.execute(req);
    }

    public HboneService getHboneService() {
        return hboneService;
    }

    public void setHboneService(HboneService hboneService) {
        this.hboneService = hboneService;
    }

    public QuerySupSignAgreementListFacade getQuerySupSignAgreementListFacade() {
        return querySupSignAgreementListFacade;
    }

    public void setQuerySupSignAgreementListFacade(QuerySupSignAgreementListFacade querySupSignAgreementListFacade) {
        this.querySupSignAgreementListFacade = querySupSignAgreementListFacade;
    }


    public KycService getKycService() {
        return kycService;
    }

    public void setKycService(KycService kycService) {
        this.kycService = kycService;
    }

    public RelatedAccountService getRelatedAccountService() {
        return relatedAccountService;
    }

    public void setRelatedAccountService(RelatedAccountService relatedAccountService) {
        this.relatedAccountService = relatedAccountService;
    }

    public QueryBalanceParamCmd getQueryBalanceParamCmd() {
        return queryBalanceParamCmd;
    }

    public void setQueryBalanceParamCmd(QueryBalanceParamCmd queryBalanceParamCmd) {
        this.queryBalanceParamCmd = queryBalanceParamCmd;
    }

    public InviteService getInviteService() {
        return inviteService;
    }

    public void setInviteService(InviteService inviteService) {
        this.inviteService = inviteService;
    }

    public SiMuIndexVo getSiMuIndexVo() {
        return siMuIndexVo;
    }

    public void setSiMuIndexVo(SiMuIndexVo siMuIndexVo) {
        this.siMuIndexVo = siMuIndexVo;
    }

    @Override
    protected void callTask() {
        RequestContextHolder.setRequestAttributes(getRequestAttributes());
        if (StringUtils.isBlank(queryBalanceParamCmd.getSubAccountId())) {
            // 是否专业投资者
            KycModel kycModel = kycService.queryKycInfo(queryBalanceParamCmd.getHboneNo());
            if (null != kycModel && null != kycModel.getInvestorType() && PRO.equals(kycModel.getInvestorType())) {
                siMuIndexVo.setIsProfessor(YesOrNoEnum.YES.getCode());
            } else {
                siMuIndexVo.setIsProfessor(YesOrNoEnum.NO.getCode());
            }
            String showRelatedAccount = relatedAccountService.showRelatedAccount(queryBalanceParamCmd.getHboneNo(), queryBalanceParamCmd.getTxAcctNo());
            siMuIndexVo.setShowRelatedAccount(showRelatedAccount);
        } else {
            siMuIndexVo.setRelatedCustName(queryBalanceParamCmd.getCustName());
            siMuIndexVo.setRelatedHboneNo(queryBalanceParamCmd.getRelatedHboneNo());
        }
        // 12.补签协议
        QuerySupSignAgreementListResponse querySupSignAgreementListResponse = queryQuerySupSignAgreementList(queryBalanceParamCmd.getTxAcctNo(), queryBalanceParamCmd.getHboneNo());
        if (querySupSignAgreementListResponse != null && CollectionUtils.isNotEmpty(querySupSignAgreementListResponse.getFundList())) {
            siMuIndexVo.setWaitSupSignAgreementNum(querySupSignAgreementListResponse.getFundList().size());
            siMuIndexVo.setWaitSupSignAgreementFundCodeList(querySupSignAgreementListResponse.getFundList().stream().map(QuerySupSignAgreementListResponse.SupSignFund::getFundCode).distinct().collect(Collectors.toList()));
            siMuIndexVo.setWaitSupSignAgreementFundList(querySupSignAgreementListResponse.getFundList().stream().map(x -> {
                WaitSupSignAgreementFundVo waitSupSignAgreementFundVo = new WaitSupSignAgreementFundVo();
                BeanUtils.copyProperties(x, waitSupSignAgreementFundVo);
                return waitSupSignAgreementFundVo;
            }).distinct().collect(Collectors.toList()));
        }
        // 13.是否存在密码,1:是,0:否
        MemberCustInfoModel custInfo = hboneService.queryCustInfoByHboneNo(queryBalanceParamCmd.getHboneNo());
        siMuIndexVo.setExistPassword(custInfo.getExistPassword() != null && custInfo.getExistPassword() ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        // 14.是有有处理的邀请
        InvitePageDto invitePageDto = inviteService.queryInviteEntryInfo(queryBalanceParamCmd.getHboneNo());
        if (invitePageDto != null && CollectionUtils.isNotEmpty(invitePageDto.getInviteEntryDtoList())) {
            siMuIndexVo.setHasInvite(YesOrNoEnum.YES.getCode());
        } else {
            siMuIndexVo.setHasInvite(YesOrNoEnum.NO.getCode());
        }
    }
}
