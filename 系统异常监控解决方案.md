# 系统异常监控解决方案

## 问题描述

在 `SystemExceptionMonitorInterceptorTest` 类中，`org.mockito.MockedStatic` 导入爆红，无法正常使用。

同时发现了一个更重要的问题：异常被 CGI 框架的全局异常处理器提前处理，导致拦截器的 `afterCompletion` 方法中 `ex` 参数为 `null`，无法进行异常监控。

## 问题原因

### 1. MockedStatic 版本问题

项目中存在 Mockito 版本冲突：
- **项目显式依赖**：Mockito 2.23.4（在 `cgi-simu/pom.xml` 中明确指定）
- **Spring Boot 2.3.12.RELEASE 自带**：Mockito 3.3.3（通过 `spring-boot-starter-test` 引入）

`MockedStatic` 是 Mockito 3.4.0+ 版本才引入的功能。

### 2. 异常监控失效问题

从日志分析可以看出：
```json
{"code":"1999","body":null,"desc":"系统错误，请联系好买","timestampServer":"1667976831730"}
```

异常被 `CGIExceptionResolver` 等全局异常处理器提前处理，转换成标准响应格式，导致拦截器无法捕获到原始异常。

## 解决方案

### 方案1：升级 Mockito 版本

移除项目中显式的 Mockito 版本声明，使用 Spring Boot 自带的 Mockito 3.3.3 版本。

#### 修改内容

在 `cgi-ehowbuy/cgi-simu/pom.xml` 中：

```xml
<!-- 移除显式的 mockito-core 依赖，使用 Spring Boot 自带的版本 (3.3.3) -->
<!-- 这样可以支持 MockedStatic 功能 -->
<!--
<dependency>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-core</artifactId>
    <version>2.23.4</version>
    <scope>test</scope>
</dependency>
-->
```

### 方案2：增强异常监控机制

创建了一个双重监控机制：

#### 2.1 保留原有拦截器（处理直接异常）

`SystemExceptionMonitorInterceptor` 继续处理直接抛出的异常：

```java
@Override
public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
    // 只处理直接抛出的异常
    // 被全局异常处理器处理的异常由 SystemExceptionMonitorFilter 负责检测
    if (ex != null && isSystemException(ex)) {
        sendSystemExceptionAlert(request, ex);
    }
}
```

#### 2.2 新增过滤器（检测响应体错误码）

`SystemExceptionMonitorFilter` 通过检查响应体中的错误码来检测系统异常：

```java
// 检查是否包含系统错误码
if (jsonResponse != null && SYSTEM_ERROR_CODE.equals(jsonResponse.getString("code"))) {
    // 发送系统异常告警
    sendSystemExceptionAlert(request, jsonResponse, responseBody);
}
```

#### 2.3 配置过滤器

`SystemExceptionMonitorConfig` 注册过滤器：

```java
@Bean
public FilterRegistrationBean<SystemExceptionMonitorFilter> systemExceptionMonitorFilter() {
    FilterRegistrationBean<SystemExceptionMonitorFilter> registration = new FilterRegistrationBean<>();
    registration.setFilter(new SystemExceptionMonitorFilter());
    registration.addUrlPatterns("/simu/*");
    registration.setName("systemExceptionMonitorFilter");
    registration.setOrder(1); // 设置较高优先级
    return registration;
}
```

## 实现细节

### 1. 文件结构

```
cgi-ehowbuy/cgi-simu/src/main/java/com/howbuy/cgi/trade/simu/
├── interceptor/
│   └── SystemExceptionMonitorInterceptor.java (修改)
├── filter/
│   └── SystemExceptionMonitorFilter.java (新增)
└── config/
    └── SystemExceptionMonitorConfig.java (新增)
```

### 2. 监控机制

- **拦截器**：处理直接抛出的异常（detectionMethod: "DIRECT_EXCEPTION"）
- **过滤器**：检测响应体中的系统错误码（detectionMethod: "RESPONSE_BODY_ANALYSIS"）

### 3. 告警信息

两种监控方式都会发送详细的告警信息，包括：
- 请求基本信息（URI、方法、TraceId、IP等）
- 异常/错误信息
- 过滤后的请求参数
- 检测方式标识

## 验证方法

### 1. 测试直接异常（拦截器处理）

访问会直接抛出异常的接口，验证拦截器是否正常工作。

### 2. 测试系统错误响应（过滤器处理）

访问 `/simu/test/nullpointer.htm` 等测试接口，验证过滤器是否能检测到响应体中的 `code=1999`。

### 3. 日志验证

查看日志中是否有对应的告警信息：
- 拦截器：`系统异常告警已发送: {...}`
- 过滤器：`系统异常告警已发送 (通过响应体检测): {...}`

## 优势

1. **双重保障**：既处理直接异常，也检测被处理后的系统错误
2. **完整信息**：包含详细的请求和异常信息
3. **敏感信息保护**：自动过滤密码、手机号等敏感参数
4. **易于扩展**：可以轻松添加新的错误码检测规则

## 注意事项

1. **性能影响**：过滤器会缓存响应内容，对大响应可能有性能影响
2. **内存使用**：ContentCachingResponseWrapper 会将响应内容缓存在内存中
3. **测试环境**：确保在生产环境中移除或禁用测试控制器

## 后续优化建议

1. **日志集成**：可以集成日志查询API，从日志中提取更详细的异常信息
2. **错误码扩展**：根据业务需要添加更多系统错误码的检测
3. **告警频率控制**：在运维监控系统中配置告警频率限制
4. **性能优化**：对于大响应，可以考虑只检查响应头或前几KB内容
