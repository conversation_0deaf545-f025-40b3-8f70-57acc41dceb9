/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.service.relatedaccount;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.common.enums.InviteStatEnum;
import com.howbuy.acccenter.common.enums.RelatedAccountRoleEnum;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.QueryRelatedAccountInviteFacade;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.QueryRelatedAccountInviteRequest;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.QueryRelatedAccountInviteResponse;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.RelatedAccountBean;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.RelatedAccountInviteBean;
import com.howbuy.acccenter.facade.trade.relatedaccount.*;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.common.util.RemoteUtil;
import com.howbuy.cgi.trade.ccms.simu.SimuCcmsServiceRegister;
import com.howbuy.cgi.trade.simu.model.dto.InvitePageDto;
import com.howbuy.cgi.trade.simu.model.dto.relatedaccount.InviteEntryDto;
import com.howbuy.cgi.trade.simu.model.relatedaccount.QueryInvitesModel;
import com.howbuy.cgi.trade.simu.util.RelatedAccoutUtils;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.trade.account.model.hbone.MemberCustInfoModel;
import com.howbuy.trade.account.service.hbone.HboneService;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 关联账户邀请服务
 *
 * <AUTHOR>
 * @date 2021/7/19 15:11
 * @since JDK 1.8
 */
@Service("inviteService")
public class InviteService {

    private static final Logger logger = LogManager.getLogger();

    @Autowired
    @Qualifier("simu.queryRelatedAccountInviteFacade")
    private QueryRelatedAccountInviteFacade queryRelatedAccountInviteFacade;
    @Autowired
    @Qualifier("simu.addSubRelatedAccountCheckFacade")
    private AddSubRelatedAccountCheckFacade addSubRelatedAccountCheckFacade;
    @Autowired
    @Qualifier("simu.generateInviteCodeFacade")
    private GenerateInviteCodeFacade generateInviteCodeFacade;
    @Autowired
    @Qualifier("simu.relateAccountAgreementSignFacade")
    private RelateAccountAgreementSignFacade relateAccountAgreementSignFacade;
    @Autowired
    @Qualifier("simu.checkInviteCodeFacade")
    private CheckInviteCodeFacade checkInviteCodeFacade;
    @Autowired
    @Qualifier("simu.addSubRelatedAccountFacade")
    private AddSubRelatedAccountFacade addSubRelatedAccountFacade;
    @Autowired
    private RelatedAccountService relatedAccountService;
    @Autowired
    private HboneService hboneService;
    @Autowired
    private SimuCcmsServiceRegister simuCcmsServiceRegister;
    @Autowired
    private MessageService messageService;

    /**
     * 查询邀请中列表
     *
     * @param model
     * @return java.util.List<com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.RelatedAccountInviteBean>
     * @author: huaqiang.liu
     * @date: 2021/7/13 16:53
     * @since JDK 1.8
     */
    public List<RelatedAccountInviteBean> queryInvites(QueryInvitesModel model) {
        QueryRelatedAccountInviteRequest request = new QueryRelatedAccountInviteRequest();
        request.setRelatedAccountId(model.getAccountId());
        request.setInviteId(model.getInviteId());
        request.setSubHboneNo(model.getSubHboneNo());
        if (model.getUsed() != null) {
            request.setInviteStat(model.getUsed() ? InviteStatEnum.INVITE_SUCC_1 : InviteStatEnum.INVITE_ING_0);
        }
        QueryRelatedAccountInviteResponse response = queryRelatedAccountInviteFacade.execute(request);
        if (response == null || !RemoteUtil.isSuccess(response.getReturnCode())) {
            logger.error("queryInvites fail. accountId:{} res:{}", model.getAccountId(), JSON.toJSONString(response));
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }

        List<RelatedAccountInviteBean> list = response.getRelatedAccountInviteBeanList();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        } else {
            return list;
        }
    }

    /**
     * 查询邀请码查看入口信息
     *
     * @param hboneNo
     * @return com.howbuy.cgi.trade.simu.model.dto.relatedaccount.InviteEntryDto
     * @author: huaqiang.liu
     * @date: 2025/07/28
     * @since JDK 1.8
     */
    public InvitePageDto queryInviteEntryInfo(String hboneNo) {
        logger.info("查询邀请码查看入口信息,hboneNo={}", hboneNo);
        InvitePageDto invitePageDto = new InvitePageDto();
        // 查询当前用户的所有有效邀请
        QueryInvitesModel model = new QueryInvitesModel();
        model.setSubHboneNo(hboneNo);
        model.setUsed(false);
        List<RelatedAccountInviteBean> invites = this.queryInvites(model);
        if (CollectionUtils.isEmpty(invites)) {
            logger.info("查询邀请码查看入口信息,没有待处理的邀请");
            return invitePageDto;
        }
        // 按优先级排序：未过期 > 已过期，时间倒序
        List<InviteEntryDto> inviteEntryDtoList = selectTargetInvite(invites);
        if(!CollectionUtils.isEmpty(inviteEntryDtoList)){
            invitePageDto.setInviteEntryDtoList(Collections.singletonList(inviteEntryDtoList.get(0)));
        }
        logger.info("查询邀请码查看入口信息,结果,hboneNo={},invitePageDto={}", hboneNo, JSON.toJSONString(invitePageDto));
        return invitePageDto;
    }

    /**
     * 选择目标邀请
     *
     * @param invites
     * @return com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.RelatedAccountInviteBean
     * @author: huaqiang.liu
     * @date: 2025/07/28
     * @since JDK 1.8
     */
    private List<InviteEntryDto> selectTargetInvite(List<RelatedAccountInviteBean> invites) {
        List<InviteEntryDto> showList = new ArrayList<>();
        String currentDt = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD);
        for (RelatedAccountInviteBean invite : invites) {
            // 过期日期日期,展示的时候,如果过期天>当天,就不展示,否则展示
            int delayMinutes = StringUtils.isBlank(invite.getPeriodOfValidity()) ? 0 : Integer.parseInt(invite.getPeriodOfValidity());
            if (invite.getInviteDate() == null) {
                logger.error("邀请日期为空,inviteId={}", invite.getInviteId());
                continue;
            }
            Date limitDate = DateUtils.addMinutes(invite.getInviteDate(), delayMinutes);
            String expireDt = DateUtils.formatToString(limitDate, DateUtils.YYYYMMDD);
            if (currentDt.compareTo(expireDt) <= 0) {
                InviteEntryDto inviteEntryDto = buildInviteEntry(invite);
                showList.add(inviteEntryDto);
            }

        }
        return showList;
    }


    /**
     * 构建邀请信息
     *
     * @param invite
     * @return 邀请信息
     */
    private InviteEntryDto buildInviteEntry(RelatedAccountInviteBean invite) {
        InviteEntryDto inviteEntryDto = new InviteEntryDto();
        inviteEntryDto.setInviteId(invite.getInviteId());
        if (invite.getInviteDate() != null) {
            inviteEntryDto.setInviterTime(DateUtils.formatToString(invite.getInviteDate(), DateUtils.YYYYMMDDHHMMSS));
            // 判断过期状态
            Date now = new Date();
            int delayMinutes = StringUtils.isBlank(invite.getPeriodOfValidity()) ? 0 : Integer.parseInt(invite.getPeriodOfValidity());
            Date limitDate = DateUtils.addMinutes(invite.getInviteDate(), delayMinutes);
            inviteEntryDto.setHasExpired(limitDate.after(now) ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
            inviteEntryDto.setExpireTime(DateUtils.formatToString(limitDate, DateUtils.YYYYMMDDHHMMSS));
        }
        // 查询邀请人姓名
        try {
            RelatedAccountBean account = relatedAccountService.queryFamilyRelatedAccount(null, invite.getRelatedAccountId());
            if (account != null) {
                MemberCustInfoModel custInfo = hboneService.queryCustInfoByHboneNo(account.getHboneNo());
                if (custInfo != null) {
                    inviteEntryDto.setInviterName(custInfo.getCustName());
                }
            }
        } catch (Exception e) {
            logger.error("查询邀请人姓名失败", e);
        }
        return inviteEntryDto;
    }

    private String getInviteUrl(String inviteId, Date inviteDate) {
        // 生成链接
        String url = simuCcmsServiceRegister.getInviteCodeUrl() + "?inviteId=" + inviteId;
        // 转成短链接
        Date urlEndTime = DateUtils.addMonthOfYear(inviteDate, 12);
        String shortLink = messageService.getShortLink(url, urlEndTime);
        if (StringUtils.isBlank(shortLink)) {
            logger.error("生成短链接失败");
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }
        return url;
    }

    /**
     * 查询有效的邀请中列表
     *
     * @return java.util.List<com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.RelatedAccountInviteBean>
     * @author: huaqiang.liu
     * @date: 2021/7/13 16:53
     * @since JDK 1.8
     */
    public List<RelatedAccountInviteBean> queryInvitesEffective(QueryInvitesModel model) {
        List<RelatedAccountInviteBean> inviteList = queryInvites(model);
        if (CollectionUtils.isEmpty(inviteList)) {
            return null;
        } else {
            Date now = new Date();
            List<RelatedAccountInviteBean> list = new ArrayList<>();
            for (RelatedAccountInviteBean bean : inviteList) {
                Date limitDate = DateUtils.addMinutes(bean.getInviteDate(), Integer.parseInt(bean.getPeriodOfValidity()));
                if (limitDate.compareTo(now) <= 0) {
                    // 跳过已过期的
                    continue;
                }
                list.add(bean);
            }
            return list.isEmpty() ? null : list;
        }
    }

    /**
     * 验证是否可以邀请
     *
     * @param accountId
     * @param subHboneNo
     * @param role
     * @return java.lang.String
     * @author: huaqiang.liu
     * @date: 2021/7/15 20:51
     * @since JDK 1.8
     */
    public String checkForInvite(String accountId, String subHboneNo, String role) {
        AddSubRelatedAccountCheckRequest request = new AddSubRelatedAccountCheckRequest();
        request.setRelatedAccountId(accountId);
        request.setRelatedAccountRole(RelatedAccountRoleEnum.getValue(role));
        request.setSubHboneNo(subHboneNo);
        AddSubRelatedAccountCheckResponse response = addSubRelatedAccountCheckFacade.execute(request);
        if (response == null) {
            logger.error("checkForInvite fail. accountId:{} subHboneNo:{} role:{} res:{}", accountId, subHboneNo, role, JSON.toJSONString(response));
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }
        return response.getReturnCode();
    }

    /**
     * 生成邀请码
     *
     * @param accountId
     * @param subHboneNo
     * @param role
     * @return java.lang.String
     * @author: huaqiang.liu
     * @date: 2021/7/15 20:57
     * @since JDK 1.8
     */
    public GenerateInviteCodeResponse generateInviteCode(String accountId, String subHboneNo, String role) {
        GenerateInviteCodeRequest request = new GenerateInviteCodeRequest();
        request.setRelatedAccountId(accountId);
        request.setRelatedAccountRole(RelatedAccountRoleEnum.getValue(role));
        request.setSubHboneNo(subHboneNo);
        GenerateInviteCodeResponse response = generateInviteCodeFacade.execute(request);
        if (response == null || !RemoteUtil.isSuccess(response.getReturnCode())) {
            logger.error("generateInviteCode fail. accountId:{} subHboneNo:{} res:{}", accountId, subHboneNo, JSON.toJSONString(response));
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }
        return response;
    }

    /**
     * 校验邀请码
     *
     * @param inviteId
     * @param inviteCode
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/7/19 14:44
     * @since JDK 1.8
     */
    public String useInviteCode(String inviteId, String inviteCode) {
        // 校验验证码
        CheckInviteCodeRequest checkReq = new CheckInviteCodeRequest();
        checkReq.setInviteId(inviteId);
        checkReq.setInviteCode(inviteCode);
        CheckInviteCodeResponse checkRes = checkInviteCodeFacade.execute(checkReq);
        // 邀请码错误
        // 5220184  关联账户邀请不存在
        // 5220185 关联账户邀请码不一致
        // 5220186 关联账户邀请重复核验
        if (checkRes != null && RelatedAccoutUtils.equalsAny(checkRes.getReturnCode(), "5220184", "5220185", "5220186")) {
            throw new BizException(BizErrorEnum.INVITE_CODE_ERROR.getCode(), BizErrorEnum.INVITE_CODE_ERROR.getDesc());
        }
        // 邀请码已过期
        // 5220187 关联账户邀请码已过期
        if (checkRes != null && RelatedAccoutUtils.equalsAny(checkRes.getReturnCode(), "5220187")) {
            throw new BizException(BizErrorEnum.INVITE_CODE_EXPIRED.getCode(), BizErrorEnum.INVITE_CODE_EXPIRED.getDesc());
        }
        if (checkRes == null || !RemoteUtil.isSuccess(checkRes.getReturnCode())) {
            logger.error("checkInviteCode fail. inviteId:{} inviteCode:{} res:{}", inviteId, inviteCode, JSON.toJSONString(checkRes));
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }
        // 添加子账户
        AddSubRelatedAccountRequest addReq = new AddSubRelatedAccountRequest();
        addReq.setRelatedAccountId(checkRes.getRelatedAccountId());
        addReq.setRelatedAccountRole(checkRes.getRelatedAccountRole());
        addReq.setSubHboneNo(checkRes.getSubHboneNo());
        AddSubRelatedAccountResponse addRes = addSubRelatedAccountFacade.execute(addReq);
        if (addRes == null || !RemoteUtil.isSuccess(addRes.getReturnCode())) {
            logger.error("checkInviteCode fail. accountId:{} subHboneNo:{} res:{}", checkRes.getRelatedAccountId(), checkRes.getSubHboneNo(), JSON.toJSONString(addRes));
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }

        return checkRes.getSubHboneNo();
    }

    /**
     * 签署邀请码查看协议
     *
     * @param hboneNo
     * @param inviteId
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/7/16 17:23
     * @since JDK 1.8
     */
    public void signAgreement(String hboneNo, String inviteId) {
        RelateAccountAgreementSignRequest request = new RelateAccountAgreementSignRequest();
        request.setSubHboneNo(hboneNo);
        request.setInviteId(inviteId);
        RelateAccountAgreementSignResponse response = relateAccountAgreementSignFacade.execute(request);
        if (response == null || !RemoteUtil.isSuccess(response.getReturnCode())) {
            logger.error("signAgreement fail. hboneNo:{} inviteId:{} res:{}", hboneNo, inviteId, JSON.toJSONString(response));
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), BizErrorEnum.SYSTEM_ERROR.getDesc());
        }
    }

}