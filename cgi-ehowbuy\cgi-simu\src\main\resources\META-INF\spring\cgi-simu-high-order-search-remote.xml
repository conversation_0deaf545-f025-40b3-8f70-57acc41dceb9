<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

	<!-- 私募中台-交易查询 -->
	<dubbo:reference id="simu.queryAcctBalanceFacade" interface="com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryAcctBalanceNewFacade" interface="com.howbuy.tms.high.orders.facade.search.queryacctbalanceNew.QueryAcctBalanceNewFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryFinReceiptFacade" interface="com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryAcctBalanceDtlFacade" interface="com.howbuy.tms.high.orders.facade.search.queryacctbalancedtl.QueryAcctBalanceDtlFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryEsignatureFacade" interface="com.howbuy.tms.high.orders.facade.search.queryesignature.QueryEsignatureFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.querySuppleStatusFacade" interface="com.howbuy.tms.high.orders.facade.search.querysupplestatus.QuerySuppleStatusFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryDealOrderListFacade" interface="com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryHighDealOrderDtlFacade" interface="com.howbuy.tms.high.orders.facade.search.queryhighdealorderdtl.QueryHighDealOrderDtlFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryCustFundDivFacade" interface="com.howbuy.tms.high.orders.facade.search.querycustfunddiv.QueryCustFundDivFacade" registry="high-order-search-remote"  check="false" />
	<dubbo:reference id="simu.queryCustSignedEcontractFacade" interface="com.howbuy.tms.high.orders.facade.search.querycustsignedecontract.QueryCustSignedEcontractFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryProductQuotaFacade" interface="com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryCancelOrderListFacade" interface="com.howbuy.tms.high.orders.facade.search.querycancelorderlist.QueryCancelOrderListFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryBuyFundStatusFacade"  timeout="10000" interface="com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryRedeemFundStatusFacade"  timeout="10000" interface="com.howbuy.tms.high.orders.facade.search.queryredeemfundstatus.QueryRedeemFundStatusFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryRedeemFundListFacade" interface="com.howbuy.tms.high.orders.facade.search.queryredeemfundlist.QueryRedeemFundListFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryPreBookListFacade" interface="com.howbuy.tms.high.orders.facade.search.queryprebooklist.QueryPreBookListFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryPreBookDtlFacade" interface="com.howbuy.tms.high.orders.facade.search.queryprebookdtl.QueryPreBookDtlFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryLatelyPreBookFacade" interface="com.howbuy.tms.high.orders.facade.search.querylatelyprebook.QueryLatelyPreBookFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryCustRepurchaseProtocolFacade" interface="com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.QueryCustRepurchaseProtocolFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryHighFundInvPlanListFacade" interface="com.howbuy.tms.high.orders.facade.search.queryhighfundinvplanlist.QueryHighFundInvPlanListFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryHighFundInvPlanDtlFacade" interface="com.howbuy.tms.high.orders.facade.search.queryhighfundinvplandtl.QueryHighFundInvPlanDtlFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryFundOwnershipOrderListFacadeService" interface="com.howbuy.tms.high.orders.facade.search.queryacctownershiporder.QueryFundOwnershipOrderListFacadeService" registry="high-order-search-remote" check="false"/>
	<dubbo:reference id="simu.queryAcctBalanceTotalInfoFacade" interface="com.howbuy.tms.high.orders.facade.search.queryacctBalancetotalinfo.QueryAcctBalanceTotalInfoFacade" registry="high-order-search-remote" check="false"/>
	<dubbo:reference id="simu.queryBalanceDetailOtherInfoFacade" interface="com.howbuy.tms.high.orders.facade.search.querybalancedetailotherinfo.QueryBalanceDetailOtherInfoFacade" registry="high-order-search-remote" check="false"/>
	<dubbo:reference id="simu.queryHzBuyOrderInfoFacade" timeout="10000"   interface="com.howbuy.tms.high.orders.facade.search.queryHzBuyOrderInfo.QueryHzBuyOrderInfoFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryOnWayBalanceFacade" interface="com.howbuy.tms.high.orders.facade.search.queryOnWayOrder.QueryOnWayBalanceFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryFeeInfoFacade" interface="com.howbuy.tms.high.orders.facade.search.queryfeeinfo.QueryFeeInfoFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.queryFormTemplateFacade" interface="com.howbuy.tms.high.orders.facade.search.querysubmitform.QueryFormTemplateFacade" registry="high-order-search-remote"  check="false"/>
	<dubbo:reference id="simu.querySupSignAgreementListFacade" interface="com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListFacade" check="false" registry="high-order-search-remote"/>
	<dubbo:reference id="simu.queryEsignatureFilePathFacade" interface="com.howbuy.tms.high.orders.facade.search.queryesignaturefilepath.QueryEsignatureFilePathFacade" check="false" registry="high-order-search-remote"/>
	<dubbo:reference id="simu.queryFirstBuyFlagFacade" interface="com.howbuy.tms.high.orders.facade.search.queryFirstBuyFlagFacade.QueryFirstBuyFlagFacade" check="false" registry="high-order-search-remote"/>
	<dubbo:reference id="simu.queryLiCaiResultFacade" interface="com.howbuy.tms.high.orders.facade.search.queryLiCaiResult.QueryLiCaiResultFacade" check="false" registry="high-order-search-remote"/>

	<dubbo:reference id="simu.queryAckDealOrderFacade" interface="com.howbuy.tms.high.orders.facade.search.queryAckDealOrder.QueryAckDealOrderFacade" registry="high-order-search-remote"  check="false"/>
</beans>