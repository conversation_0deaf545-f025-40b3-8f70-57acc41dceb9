文档概况
====

|     |     |
| --- | --- |
| 文档状态 | 进行中 |
| 文档所有者 | 林纯  |
| UI设计 | 苗丹  |
| 程序猿 | 填写项目工程师名称 |
| QA  | 填写测试工程师名称 |

文档修订历史
======

| 版本  | 作者  | 工作描述 | 修改时间 |
| --- | --- | --- | --- |
| 版本  | 作者  | 工作描述 | 修改时间 |
| --- | --- | --- | --- |
| V1.0 | 林纯  | 创建文档 | 2025-06-26 |
|     |     |     |     |
|     |     |     |     |

参考文档
====

| 文档类型 | 地址  |
| --- | --- |
| 文档类型 | 地址  |
| --- | --- |
| UI地址 | [https://mastergo.com/goto/KIDmdZAc?page\_id=0:2&file=164264582776525](https://mastergo.com/goto/KIDmdZAc?page_id=0:2&file=164264582776525 邀请您进入《关联账户管理关联账户管理》，点击链接开始协作) |
| 关联需求文档 | /   |
| 参考文档 | 关联账户v1.0：[http://dms.intelnal.howbuy.com/pages/viewpage.action?pageId=29624846](http://dms.intelnal.howbuy.com/pages/viewpage.action?pageId=29624846) |

目录
==

*   1[文档概况](#id-73、短信体系改造去掉联系方式/网址等-文档概况)
*   2[文档修订历史](#id-73、短信体系改造去掉联系方式/网址等-文档修订历史)
*   3[参考文档](#id-73、短信体系改造去掉联系方式/网址等-参考文档)
*   4[目录](#id-73、短信体系改造去掉联系方式/网址等-目录)
*   5[一、总览](#id-73、短信体系改造去掉联系方式/网址等-一、总览)
    *   5.1[1.1 背景与目的](#id-73、短信体系改造去掉联系方式/网址等-1.1背景与目的)
    *   5.2[1.2 功能列表](#id-73、短信体系改造去掉联系方式/网址等-1.2功能列表)
*   6[二、需求概述 ](#id-73、短信体系改造去掉联系方式/网址等-二、需求概述)
    *   6.1[2.1 短信模版优化或功能下线 @消息中心 @账户中心 @CMS @CRM](#id-73、短信体系改造去掉联系方式/网址等-2.1短信模版优化或功能下线@消息中心@账户中心@CMS@CRM)
    *   6.2[2.2 功能优化  @H5 @消息中心 @高端中台](#id-73、短信体系改造去掉联系方式/网址等-2.2功能优化@H5@消息中心@高端中台)
        *   6.2.1[家庭账户-线上流程](#id-73、短信体系改造去掉联系方式/网址等-家庭账户-线上流程)
    *   6.3[2.3 MGM消息推送优化 @薛亦斌 @CMS @CRM](#id-73、短信体系改造去掉联系方式/网址等-2.3MGM消息推送优化@薛亦斌@CMS@CRM)
        *   6.3.1[MGM消息通知优化](#id-73、短信体系改造去掉联系方式/网址等-MGM消息通知优化)
            *   6.3.1.1[限制规则](#id-73、短信体系改造去掉联系方式/网址等-限制规则)

**一、总览**
========

**1.1 背景与目的**
-------------

发送出去的短信中，如果内容包含联系方式（手机、座机）、网址，均需要提前报备。如无报备，会被拦截发送失败。

本次改造，仅涉及短信中含有传参联系方式、网址的短信模版、及对应的功能。

**1.2 功能列表**
------------

| 模块  | 页面/功能 | 需求概述 | 平台  |
| --- | --- | --- | --- |
| 模块  | 页面/功能 | 需求概述 | 平台  |
| --- | --- | --- | --- |
| 消息中心 | 前端给客户/投顾发送的短信 | *   短信模版中涉及传参联系方式、网址：<br><br>方案一：调整传参内容<br><br>方案二：调整发送渠道<br><br>方案三：下线短信渠道<br><br>方案四：下线功能及对应发送消息 | 消息中心、账户中心、CMS、CRM |
| 关联账户 | 线上绑定家庭账户，邀请码通过短信发送，短信中带链接 | 将邀请码的入口调整至好买基金APP中 | H5、消息中心 |
| CRM站内信&企微消息通知 | 投顾名片&线上MGM功能给投顾发送的消息通知 | 1分钟内同样的消息，限制发送频率 | CMS、CRM |

**二、需求概述**
==========
**2.1 功能优化  @H5 @消息中心 @高端中台**
-----------------------------

功能点

功能说明

### 家庭账户-线上流程

**0、整体说明：**

（1）现状：客户在APP创建家庭后，邀请家庭成员，目前是通过短信邀请码方式：给被邀请人发送含有获取邀请码链接的短信。被邀请人通过短信中的链接，进行确认并获取邀请码。产线现有流程如下：

*   短信内容

业务ID：60372

模块名称：高端关联账户-邀请加入

模版内容：${custName}邀请您加入家庭账户，您可点击${URL}，查看邀请码及相关说明。

*   操作流程示例图

![](/download/attachments/101627906/image2025-6-25_16-58-46.png?version=1&modificationDate=1750841927000&api=v2)

（2）本次调整：

*   给被邀请人发送的短信内容仅做通知，不包含邀请码链接
*   邀请码链接入口调整至APP内，被邀请人通过登录APP进行确认并获取邀请码

  

**1、线上邀请流程-发送短信邀请码**

（1）入口：

| 图例  | 说明  |
| --- | --- |
| ![](/download/thumbnails/101627906/image2025-6-25_17-12-19.png?version=1&modificationDate=1750842740000&api=v2) | *   入口一：在完成角色选择后，进入邀请发送窗口，点击【发送邀请码】 |
| ![](/download/thumbnails/101627906/image2025-6-26_9-12-26.png?version=1&modificationDate=1750900346000&api=v2) | *   入口二：在邀请码核验页中，点击【重新发送】 |

（2）内容、交互、校验逻辑：维持产线逻辑

（3）校验通过后，发送短信：**@消息中心**

*   业务ID（60372）  //还是产线的这个业务ID，只是模版内容改了

1.  1.  模版内容：${custName}邀请您加入家庭账户，邀请码有效期30分钟。您可登录好买基金APP，通过“我的-私募基金-关联账户”，查看邀请码及相关说明。
        1.  ${custName}取家庭主的一账通姓名

  

**2、线上邀请流程-邀请码核验页**

（1）入口：

| 图例  | 说明  |
| --- | --- |
| ![](/download/thumbnails/101627906/image2025-6-26_9-19-10.png?version=1&modificationDate=1750900749000&api=v2) | *   入口一：提交短信邀请码发送成功后，跳转至邀请码核验页 |
| ![](/download/thumbnails/101627906/image2021-1-29%2015%3A5%3A49.png?version=1&modificationDate=1750843121000&api=v2) | *   入口二：通过关联账户管理 - 家庭账户下的邀请中成员确认，点击【立即确认】，跳转至邀请码核验页 |

（2）“邀请码核验”弹窗内容优化：

| 图例  | 说明  |
| --- | --- |
| ![](/download/thumbnails/101627906/image2025-7-1_10-8-35.png?version=1&modificationDate=1751335714000&api=v2) | *   动态文案1：邀请码已发送至【XX】“XXXXXXXXXXX”账号上，请被邀请成员登录好买基金APP获取邀请码。请您在30分钟内核验邀请码，完成家庭成员邀请！<br><br>1.  1.  【XX】：取被邀请的角色姓名<br>    2.  手机号全明文展示，取客户添加的被邀请家庭成员的手机号<br><br>*   动态文案2：“当前邀请过期时间：今日/明日 XX:XX”  //维持产线逻辑<br>*   固定文案“说明：1、请被邀请成员使用您提交的手机号登录好买基金APP，查看邀请码；2、请在邀请码有效期内尽快完成邀请码核验；3、待确认的邀请可在关联账户管理首页进行查看；4、每隔五分钟可重新发送一次邀请码” |

  

**3、线上邀请流程-邀请码查看流程**

（1）入口

| 图例  | 说明  |
| --- | --- |
| ![](/download/thumbnails/101627906/image2025-7-1_10-10-30.png?version=1&modificationDate=1751335829000&api=v2) | 0、调整前：短信<br><br>1、调整后：好买基金APP-我的-私募基金-关联账户管理-关联账户管理页：家庭关联账户模块<br><br>*   在家庭关联账户模块的底部，增加邀请码查看入口<br>*   当家庭关联账户模块显示邀请码查看入口时，“私募基金-关联账户”显示提示标签<br><br>2、邀请码查看入口前置条件：<br><br>*   登录用户存在**未过期**的家庭账户关联邀请，显示入口<br>*   登录用户存在**过期**的家庭账户关联邀请，过期时间=当天，显示入口<br>*   其他情况，隐藏入口<br>*   注：如有多条邀请，仅取一条：未过期>已过期。如有多条，仅取最近的一条<br><br>3、邀请码查看入口的文案：图标+XXX邀请您加入家庭账户+查看按键<br><br>*   图标：以UI为准<br>*   XXX：取邀请人的客户姓名，红色字显示<br>*   按键名称：立即查看<br><br>4、邀请码查看入口的交互：点击【立即查看】按键，进入邀请码查看页<br><br>5、当家庭关联账户模块显示邀请码查看入口时，“私募基金-关联账户管理”的右上角，显示提示标签![](/download/thumbnails/101627906/image2025-7-1_10-14-11.png?version=1&modificationDate=1751336051000&api=v2) |

（2）邀请码查看页  //同产线逻辑，只是APP内打开可以直接传参登录人的信息 ，打开就能直接进去，无需登录

*   校验1、校验用户是否已登录

\>>已登录，进入校验2

\>>未登录，打开登录页

*   校验2、被邀请的人填写手机号与家庭主填写的手机号是否一致

\>>不一致,页面提示，“身份核验不一致”

\>>一致，进入邀请码查看页

![](/download/attachments/101627906/image2025-6-26_11-8-50.png?version=1&modificationDate=1750907329000&api=v2)

**2.3 MGM消息推送优化 @薛亦斌 @CMS @CRM**
--------------------------------

| 功能点 | 功能说明 |
| --- | --- |
| 功能点 | 功能说明 |
| --- | --- |
| ### MGM消息通知优化<br><br>![](/download/attachments/101627906/image2025-6-30_18-9-59.png?version=1&modificationDate=1751278199000&api=v2) | 1.  **整体说明：用户在接收CRM的MGM通知消息时，若在1min内，连续接收相同【客户】、相同【关联投顾】的MGM消息通知，则限制消息发送频次。同一限制时间段内，最多发送一条消息给用户。**<br>2.  **影响消息：**<br>    1.  MGM（新客户）：消息id：200281<br>    2.  MGM（已在投顾名下）消息id：200282<br>    3.  MGM（客服名下）消息id：200302<br>    4.  MGM（其他投顾名下）消息id：200284<br>    5.  投顾开发（新客户）消息id：200301<br>    6.  投顾开发（已在投顾名下）消息id：200283<br>    7.  投顾开发（客服名下）消息id：200303<br>    8.  投顾开发（其他投顾名下）消息id：200304<br>3.  **消息关联要素**：<br>    *   相同【客户】标识；<br>    *   相同【关联投顾】标识。<br>4.  **触发场景**：用户连续接收满足上述条件的同类 MGM 通知消息。<br>5.  #### 限制规则<br>    <br>    1.  系统实时监测消息发送请求，匹配客户与关联投顾信息；<br>    2.  若 1 分钟内检测到相同客户 + 投顾的重复消息请求，仅允许第一条消息发送，后续消息进入拦截队列；<br>    3.  限制时间段结束后，频次计数重置，可重新触发消息发送。 |