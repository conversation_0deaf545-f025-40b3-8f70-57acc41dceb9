package com.howbuy.cgi.trade.simu.controller.trade;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoFacade;
import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoRequest;
import com.howbuy.acccenter.facade.query.querybankcardinfo.QueryBankCardInfoResponse;
import com.howbuy.acccenter.facade.trade.convenientconfirmformobile.ConvenientConfirmForMobileFacade;
import com.howbuy.acccenter.facade.trade.convenientconfirmformobile.ConvenientConfirmForMobileRequest;
import com.howbuy.acccenter.facade.trade.convenientconfirmformobile.ConvenientConfirmForMobileResponse;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoFacade;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoRequest;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoResponse;
import com.howbuy.acccenter.facade.trade.validatetxpassword.ValidateTxPasswordFacade;
import com.howbuy.acccenter.facade.trade.validatetxpassword.ValidateTxPasswordRequest;
import com.howbuy.acccenter.facade.trade.validatetxpassword.ValidateTxPasswordResponse;
import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.cc.hbone.request.QueryHboneInfoRequest;
import com.howbuy.cc.hbone.response.BaseHboneInfoResponse;
import com.howbuy.cc.hbone.service.QuerySingleService;
import com.howbuy.cc.message.SendMsgResult;
import com.howbuy.cgi.common.CGIConstants;
import com.howbuy.cgi.common.enums.AuthMobileVerifyFlagEnum;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.enums.ReturnCodeEnum;
import com.howbuy.cgi.common.enums.VrfyStatEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.common.remote.param.RemoteParametersProvider;
import com.howbuy.cgi.common.util.CgiParamUtil;
import com.howbuy.cgi.common.util.RequestUtil;
import com.howbuy.cgi.common.validate.ValidateUtil;
import com.howbuy.cgi.trade.ccms.simu.SimuCcmsServiceRegister;
import com.howbuy.cgi.trade.simu.common.CGISimuConstants;
import com.howbuy.cgi.trade.simu.controller.AbstractSimuCGIController;
import com.howbuy.cgi.trade.simu.dto.json.AssetCertificateStatusDto;
import com.howbuy.cgi.trade.simu.model.cmd.*;
import com.howbuy.cgi.trade.simu.model.dto.*;
import com.howbuy.cgi.trade.simu.model.vo.*;
import com.howbuy.cgi.trade.simu.service.BuyService;
import com.howbuy.cgi.trade.simu.service.SendMessageService;
import com.howbuy.cgi.trade.simu.service.relatedaccount.AccCenterService;
import com.howbuy.cgi.trade.simu.util.BusiUtil;
import com.howbuy.cgi.trade.simu.util.CacheKeyUtil;
import com.howbuy.cgi.trade.simu.util.RandomUtil;
import com.howbuy.common.security.encrypt.EncryptAlgEnum;
import com.howbuy.common.security.encrypt.EncryptUtil;
import com.howbuy.es.digestsign.authentication.CheckMobileVerificationCodeFacade;
import com.howbuy.es.digestsign.authentication.CheckMobileVerificationCodeRequest;
import com.howbuy.es.webcommon.facade.facade.EsResponse;
import com.howbuy.fbs.fbsonlinesearch.facade.query.queryisexistmultifundtxacctno.QueryIsExistsMultiFundTxAcctNoFacade;
import com.howbuy.fbs.fbsonlinesearch.facade.query.queryisexistmultifundtxacctno.QueryIsExistsMultiFundTxAcctNoRequest;
import com.howbuy.fbs.fbsonlinesearch.facade.query.queryisexistmultifundtxacctno.QueryIsExistsMultiFundTxAcctNoResponse;
import com.howbuy.interlayer.common.Constants;
import com.howbuy.interlayer.common.utils.DateUtils;
import com.howbuy.interlayer.product.enums.YesOrNoEnum;
import com.howbuy.interlayer.product.model.*;
import com.howbuy.interlayer.product.model.fund.FundEcontractRiskInfoModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.paycommon.model.enums.AuthTypeEnum;
import com.howbuy.paycommon.model.enums.BusiCodeEnum;
import com.howbuy.paycommon.model.enums.ProdLqdTypeEnum;
import com.howbuy.payonline.facade.auth.quick.QuickCardAuthRsltFacade;
import com.howbuy.payonline.facade.auth.quick.QuickCardAuthRsltRequest;
import com.howbuy.payonline.facade.auth.quick.QuickCardAuthRsltResponse;
import com.howbuy.payonline.facade.query.queryauthtype.QueryAuthTypeFacade;
import com.howbuy.payonline.facade.query.queryauthtype.QueryAuthTypeRequest;
import com.howbuy.payonline.facade.query.queryauthtype.QueryAuthTypeResponse;
import com.howbuy.payonline.facade.query.queryauthtype.QueryAuthTypeResponse.AuthTypeVo;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.IdTypeEnum;
import com.howbuy.tms.common.enums.busi.PaySignEnum;
import com.howbuy.tms.common.enums.database.*;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusFacade;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusResponse.BuyFundStatusBean;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListFacade;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListRequest;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListResponse;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListResponse.DealOrderBean;
import com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaFacade;
import com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaRequest;
import com.howbuy.tms.high.orders.facade.search.queryproductquota.QueryProductQuotaResponse;
import com.howbuy.tms.high.orders.facade.search.querysupplestatus.QuerySuppleStatusFacade;
import com.howbuy.tms.high.orders.facade.search.querysupplestatus.QuerySuppleStatusRequest;
import com.howbuy.tms.high.orders.facade.search.querysupplestatus.QuerySuppleStatusResponse;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.SendAuthMsgFacade;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.SendAuthMsgRequest;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.SendAuthMsgResponse;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.bean.ConvenientVrifyForMobileBean;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.bean.MessageCenterConterxtBean;
import com.howbuy.tms.high.orders.facade.trade.sendauthmsg.bean.QuickCardAuthContextBean;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.bean.PayInfoBean;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurmergeweb.SubsOrPurMergeWebFacade;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurmergeweb.SubsOrPurMergeWebRequest;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurmergeweb.SubsOrPurMergeWebResponse;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurweb.SubsOrPurWebFacade;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurweb.SubsOrPurWebRequest;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurweb.SubsOrPurWebResponse;
import com.howbuy.trade.account.model.accplaintext.BankAcctSensitiveModel;
import com.howbuy.trade.account.model.accplaintext.CustMobileModel;
import com.howbuy.trade.account.model.payment.CustBankModel;
import com.howbuy.trade.account.service.account.AccPlaintextServiceImpl;
import com.howbuy.trade.account.service.payment.BankCardService;
import com.howbuy.trade.common.basecommon.remote.DisCodeInvokerUtils;
import com.howbuy.trade.common.session.model.TradeSession;
import com.howbuy.trade.common.session.model.UserInfo;
import com.howbuy.trade.piggy.model.PiggyFundTxOpenCfgModel;
import com.howbuy.trade.piggy.model.SavingBoxVolDetail;
import com.howbuy.trade.piggy.service.PiggyTradeService;
import com.howbuy.web.util.WebUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @Author:jingya.xu
 * @Date:2017/7/11
 */
@Controller
public class BuyController extends AbstractSimuCGIController {

    private static final Logger LOG = LogManager.getLogger(BuyController.class);

    @Autowired
    @Qualifier("simu.highProductService")
    private HighProductService highProductService;

    @Autowired
    @Qualifier("piggyTradeServiceImpl")
    private PiggyTradeService piggyTradeService;

    @Autowired
    private BankCardService bankCardService;

    @Autowired
    @Qualifier("simu.queryDealOrderListFacade")
    private QueryDealOrderListFacade queryDealOrderListFacade;

    @Autowired
    @Qualifier("simu.quickCardAuthRsltFacade")
    private QuickCardAuthRsltFacade quickCardAuthRsltFacade;

    @Autowired
    @Qualifier("simu.subsOrPurWebFacade")
    private SubsOrPurWebFacade subsOrPurWebFacade;
    @Autowired
    @Qualifier("simu.subsOrPurMergeWebFacade")
    private SubsOrPurMergeWebFacade subsOrPurMergeWebFacade;

    @Autowired
    @Qualifier("simu.queryBankCardInfoFacade")
    private QueryBankCardInfoFacade queryBankCardInfoFacade;

    @Autowired
    @Qualifier("simu.convenientConfirmForMobileFacade")
    private ConvenientConfirmForMobileFacade convenientConfirmForMobileFacade;

    @Autowired
    @Qualifier("simu.querySuppleStatusFacade")
    private QuerySuppleStatusFacade querySuppleStatusFacade;


    @Autowired
    private QueryAuthTypeFacade queryAuthTypeFacade;

    private CacheService cacheService = CacheServiceImpl.getInstance();

    @Autowired
    @Qualifier("simu.queryBuyFundStatusFacade")
    private QueryBuyFundStatusFacade queryBuyFundStatusFacade;

    @Autowired
    @Qualifier("simu.validateTxPasswordFacade")
    private ValidateTxPasswordFacade validateTxPasswordFacade;

    @Autowired
    private SimuCcmsServiceRegister simuCcmsServiceRegister;


    @Autowired
    @Qualifier("simu.queryProductQuotaFacade")
    private QueryProductQuotaFacade queryProductQuotaFacade;


    @Autowired
    @Qualifier("simu.kycInfoFacade")
    private KycInfoFacade kycInfoFacade;

    @Autowired
    @Qualifier("simu.sendAuthMsgFacade")
    private SendAuthMsgFacade sendAuthMsgFacade;

    @Autowired
    @Qualifier("simu.querySingleService")
    private QuerySingleService querySingleService;

    @Autowired
    @Qualifier("simu.checkMobileVerificationCodeFacade")
    private CheckMobileVerificationCodeFacade checkMobileVerificationCodeFacade;

    @Autowired
    private QueryIsExistsMultiFundTxAcctNoFacade queryIsExistsMultiFundTxAcctNoFacade;
    @Autowired
    private AccPlaintextServiceImpl accPlaintextService;
    @Autowired
    private SendMessageService sendMessageService;
    @Autowired
    private BuyService buyService;
    @Autowired
    private AccCenterService accCenterService;

    // e签宝校验短信验证码成功
    private static final String ES_VALIDATE_CODE_SUCC = "0";

    private static final String ERROR_CODE = "5220046";


    /**
     * @api {GET} /simu/trade/buylist.htm query
     * @apiVersion 1.0.0
     * @apiGroup BuyController
     * @apiName query
     * @apiDescription 查询可购买的产品列表(支持分页查询)
     * @apiParam {String} [fundCodeOrNameOrAbbr]  产品搜索字符串
     * @apiParam {String} [productType] 产品类型
     * @apiParam {String} [productSaleSource] 产品销售来源 0-好买 1-海外 2-其他
     * @apiParam {int} [targetpage] 页码
     * @apiParam {int} [pageSize]  分页大小，默认为10
     * @apiSuccess (响应结果) {Array} fundList 产品列表
     * @apiSuccess (响应结果) {String} fundList.fundCode 基金代码
     * @apiSuccess (响应结果) {String} fundList.disCode 分销渠道
     * @apiSuccess (响应结果) {String} fundList.fundName 基金名称
     * @apiSuccess (响应结果) {String} fundList.productPinyinName 基金全拼(当存在基金档案页的时候,会返url)
     * @apiSuccess (响应结果) {String} fundList.purchaseStatus 购买状态
     * @apiSuccess (响应结果) {String} fundList.productType 产品类型
     * @apiSuccess (响应结果) {Number} fundList.investMinAmount 投资起点
     * @apiSuccess (响应结果) {String} fundList.isBook 是否支持预约购买
     * @apiSuccess (响应结果) {String} fundList.openStartDt 开放起始日
     * @apiSuccess (响应结果) {String} fundList.openEndDt 开放截止日
     * @apiSuccess (响应结果) {String} fundList.payEndDate 打款截止日
     * @apiSuccess (响应结果) {String} fundList.appointmentStartDt 预约申请开始日
     * @apiSuccess (响应结果) {String} fundList.appointmentEndDt 预约申请截止日
     * @apiSuccess (响应结果) {String} fundList.cptjly 评论
     * @apiSuccess (响应结果) {String} fundList.zsbz 重点持营标志1-是
     * @apiSuccess (响应结果) {String} fundList.dbFundType DB产品类型
     * @apiSuccess (响应结果) {String} fundList.webUrl 档案页跳转
     * @apiSuccess (响应结果) {String} fundList.productSaleSource 产品销售来源
     * @apiSuccess (响应结果) {String} fundList.managerAttribute 管理人属性 1-其他；2-私募；3-资管
     * @apiSuccess (响应结果) {Number} sumNum 总条数
     * @apiSuccessExample 响应结果示例
     * {"fundList":[{"cptjly":"e5bGbnlfKg","payEndDate":"8hN3h8","openStartDt":"9","zsbz":"e","disCode":"tq6hps4W","appointmentStartDt":"oxRgpB","appointmentEndDt":"47Uu","dbFundType":"WcvlnA0bnp","investMinAmount":5487.399592924974,"isBook":"hFcyz","fundCode":"6eSv","webUrl":"0pI7g1ikI","productSaleSource":"GJSkZ0icc","purchaseStatus":"PVQ92qC1Sh","productPinyinName":"8PaibUlRC","openEndDt":"W","fundName":"9KINdve2I","managerAttribute":"Qj9dF","productType":"6vL"}],"sumNum":9576}
     */
    @RequestMapping("/simu/trade/buylist.htm")
    public void query(HttpServletRequest request, HttpServletResponse response) throws Exception {
        BuyListCmd cmd = getCommand(BuyListCmd.class);
        log.info("BuyController-query,请求入参:{}", JSON.toJSONString(cmd));
        ValidateUtil.assertValid(cmd);
        TradeSession loginInfo = this.getCustSession();
        BuyListDto dto = buyService.queryBuyList(cmd, loginInfo, request);
        write(dto, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }


    /**
     * @api {GET} /simu/trade/buy.htm buy
     * @apiVersion 1.0.0
     * @apiGroup BuyController
     * @apiName buy
     * @apiDescription 购买第一步
     * @apiParam (请求参数) {String} fundCode 基金代码
     * @apiParam (请求参数) {String} tokenId 客户端token
     * @apiParam (请求参数) {String} corpId 商户号
     * @apiParam (请求参数) {String} actionId 活动号
     * @apiParam (请求参数) {String} operIp IP
     * @apiParam (请求参数) {String} disCode 分销代码
     * @apiParam (请求参数) {String} outletCode 分销网点号
     * @apiParamExample 请求参数示例
     * tokenId=4&corpId=RSggyBZn&fundCode=jxgJjvfNX&operIp=tVbB1U&actionId=ssidGJ1F&disCode=gdxGizHsOO&outletCode=Nu0H
     * @apiSuccess (响应结果) {Object} kycResponse kyc信息
     * @apiSuccess (响应结果) {String} kycResponse.investorType 投资者类型(专业/普通): PRO("PRO","专业"), NORMAL("NORMAL","普通");
     * @apiSuccess (响应结果) {String} kycResponse.riskToleranceLevel 风险承受能力评估级别 0，1,2,3,4,5 没有做过的情况下为null
     * @apiSuccess (响应结果) {String} isBought 1-首次购买; 2-追加购买
     * @apiSuccess (响应结果) {String} isHasNopayAmt 1-真首单; 2-伪首单
     * @apiSuccess (响应结果) {String} openStartDt 开放起始日
     * @apiSuccess (响应结果) {String} openEndDt 开放截止日
     * @apiSuccess (响应结果) {String} appointmentStartDt 预约申请开始日
     * @apiSuccess (响应结果) {String} appointmentEndDt 预约申请截止日
     * @apiSuccess (响应结果) {String} payEndDate 打款截止日
     * @apiSuccess (响应结果) {String} shareClass 份额类型 ，A-前收费；B-后收费
     * @apiSuccess (响应结果) {String} productChannel 产品交易通道 产品通道：1 好买创新、2 创昱达、3 群济、4 好买储蓄罐、5 好买公募 6-高端公募
     * @apiSuccess (响应结果) {Object} highProductBaseModel 产品信息
     * @apiSuccess (响应结果) {String} highProductBaseModel.eContract 是否支持电子合同 1支持 0 是不支持
     * @apiSuccess (响应结果) {String} highProductBaseModel.fundCode 基金代码
     * @apiSuccess (响应结果) {String} highProductBaseModel.fundName 基金名称
     * @apiSuccess (响应结果) {String} highProductBaseModel.fundType 基金类型
     * @apiSuccess (响应结果) {String} highProductBaseModel.fundSubType 基金二级类型
     * @apiSuccess (响应结果) {String} highProductBaseModel.taCode TA代码
     * @apiSuccess (响应结果) {String} highProductBaseModel.dfltDivMode 基金默认分红方式
     * @apiSuccess (响应结果) {String} highProductBaseModel.suppleSubsRule 追加申购判断规则
     * @apiSuccess (响应结果) {Number} highProductBaseModel.minAcctVol 最低持有份额
     * @apiSuccess (响应结果) {String} highProductBaseModel.fundRiskLevel 基金风险等级
     * @apiSuccess (响应结果) {String} highProductBaseModel.fundAttrPinyin 基金简称拼音
     * @apiSuccess (响应结果) {String} highProductBaseModel.mainFundCode 主基金代码
     * @apiSuccess (响应结果) {Number} highProductBaseModel.distributeSize 可销售规模
     * @apiSuccess (响应结果) {String} highProductBaseModel.endTm 交易截止时间
     * @apiSuccess (响应结果) {String} highProductBaseModel.ipoStartDt 募集开始日期
     * @apiSuccess (响应结果) {String} highProductBaseModel.ipoEndDt 募集结束日期
     * @apiSuccess (响应结果) {String} highProductBaseModel.fundAttr 基金简称
     * @apiSuccess (响应结果) {String} highProductBaseModel.redeOpenTerm 产品开放周期
     * @apiSuccess (响应结果) {String} highProductBaseModel.fundOpenMode 产品开放类型
     * @apiSuccess (响应结果) {String} highProductBaseModel.openDt 开通日期
     * @apiSuccess (响应结果) {String} highProductBaseModel.openFlag 开通标志
     * @apiSuccess (响应结果) {String} highProductBaseModel.fundClass 基金类别
     * @apiSuccess (响应结果) {Number} highProductBaseModel.prodRollRange 滚动产品期限
     * @apiSuccess (响应结果) {Number} highProductBaseModel.prodSubDays 滚动产品认申购开放天数
     * @apiSuccess (响应结果) {Number} highProductBaseModel.prodRedmDays 滚动产品赎回开放天数
     * @apiSuccess (响应结果) {String} highProductBaseModel.holidayStrategy 遇节假日策略
     * @apiSuccess (响应结果) {Number} highProductBaseModel.prodDiffer 产品极差
     * @apiSuccess (响应结果) {String} highProductBaseModel.shareClass 份额类型
     * @apiSuccess (响应结果) {String} highProductBaseModel.taName TA名称
     * @apiSuccess (响应结果) {Number} highProductBaseModel.netMinAppAmt 首次最低申请金额（净购买金额）
     * @apiSuccess (响应结果) {Number} highProductBaseModel.netMinSuppleAmt 最低追加申请金额（净追加金额）
     * @apiSuccess (响应结果) {Number} highProductBaseModel.confirmDays 确认天数
     * @apiSuccess (响应结果) {String} highProductBaseModel.directOpenFlag *      定向客户开放标识（预约码交易）
     * @apiSuccess (响应结果) {String} highProductBaseModel.supportAdvanceFlag *      是否支持预约标识
     * @apiSuccess (响应结果) {String} highProductBaseModel.feeCalMode *      手续费计算类型
     * @apiSuccess (响应结果) {String} highProductBaseModel.multiCardFlag *      是否支持多卡
     * @apiSuccess (响应结果) {String} highProductBaseModel.redeemMemo *      赎回备注
     * @apiSuccess (响应结果) {String} highProductBaseModel.riskNoticeUrl *      产品风险揭示书访问路径
     * @apiSuccess (响应结果) {String} highProductBaseModel.elecContractUrl *      产品电子合同访问路径
     * @apiSuccess (响应结果) {String} highProductBaseModel.fundWebUrl *      基金产品网页链接
     * @apiSuccess (响应结果) {Number} highProductBaseModel.calmTime *      冷静期
     * @apiSuccess (响应结果) {String} highProductBaseModel.saleAgreementUrl *      销售协议书URL
     * @apiSuccess (响应结果) {String} highProductBaseModel.supplyAgreementUrl1 销售补充协议URL1
     * @apiSuccess (响应结果) {String} highProductBaseModel.supplyAgreementUrl2 销售补充协议URL2
     * @apiSuccess (响应结果) {String} highProductBaseModel.supplyAgreementUrl3 *      销售补充协议URL3
     * @apiSuccess (响应结果) {String} highProductBaseModel.supplyAgreementUrl4 *      销售补充协议URL4
     * @apiSuccess (响应结果) {String} highProductBaseModel.supplyAgreementUrl5 *      销售补充协议URL5
     * @apiSuccess (响应结果) {String} highProductBaseModel.supplyAgreementUrl6 *      销售补充协议URL6
     * @apiSuccess (响应结果) {Array} highProductBaseModel.supplyAgreementUrlList 销售补充协议列表
     * @apiSuccess (响应结果) {String} highProductBaseModel.supplyAgreementUrlList.id
     * @apiSuccess (响应结果) {String} highProductBaseModel.supplyAgreementUrlList.url
     * @apiSuccess (响应结果) {String} highProductBaseModel.paymentBankChangeUrl 监管行切换协议url
     * @apiSuccess (响应结果) {String} highProductBaseModel.productClass *      产品类别
     * @apiSuccess (响应结果) {String} highProductBaseModel.isSupSelfDrawing 支付方式：是否支持自划款 0-不支持 1-支持
     * @apiSuccess (响应结果) {String} highProductBaseModel.isSupAgentDrawing *      支付方式：是否支持代划款 0-不支持 1-支持
     * @apiSuccess (响应结果) {String} highProductBaseModel.isSupPiggy *      支付方式：是否支持储蓄罐支付 0-不支持 1-支持
     * @apiSuccess (响应结果) {String} highProductBaseModel.redeemDirectionIsSupCardFlag *      赎回去向是否支持银行卡 0-不支持 1-支持
     * @apiSuccess (响应结果) {String} highProductBaseModel.redeemDirectionIsSupCxgFlag *      赎回去向是否支持储蓄罐 0-不支持 1-支持
     * @apiSuccess (响应结果) {String} highProductBaseModel.isSupCounter 代销关系标识 是否支持好买柜台 0-不支持 1-支持
     * @apiSuccess (响应结果) {String} highProductBaseModel.isSupWeb *      代销关系标识 是否支持好买网站 0-不支持 1-支持
     * @apiSuccess (响应结果) {String} highProductBaseModel.testForceMatchFlag *      风险测评强制匹配开关 0-能 1-不能
     * @apiSuccess (响应结果) {String} highProductBaseModel.shareCtrlDate *      份额控制日期
     * @apiSuccess (响应结果) {String} highProductBaseModel.productChannel *      产品交易通道
     * @apiSuccess (响应结果) {String} highProductBaseModel.divMethodFlag *      是否允许修改分红方式
     * @apiSuccess (响应结果) {Number} highProductBaseModel.totalPlaces *      总人数
     * @apiSuccess (响应结果) {String} highProductBaseModel.ageControlFlag *      年龄控制标识 0-否 1-是
     * @apiSuccess (响应结果) {Number} highProductBaseModel.maxAge *      最大年龄
     * @apiSuccess (响应结果) {Number} highProductBaseModel.minAge *      最小年龄
     * @apiSuccess (响应结果) {Number} highProductBaseModel.purConfirmDays *      申购确认T+N
     * @apiSuccess (响应结果) {Number} highProductBaseModel.redeConfirmDays *      赎回确认T+N
     * @apiSuccess (响应结果) {Number} highProductBaseModel.redePaymentDays *      赎回到账天数
     * @apiSuccess (响应结果) {Number} highProductBaseModel.totalBalance
     * @apiSuccess (响应结果) {String} highProductBaseModel.structureFlag 结构型产品标识，0-非结构型；1-结构型
     * @apiSuccess (响应结果) {String} highProductBaseModel.establishDt 成立日期
     * @apiSuccess (响应结果) {String} highProductBaseModel.redeemType 是否控制份额退出
     * @apiSuccess (响应结果) {String} highProductBaseModel.sacFundCode 协会产品代码
     * @apiSuccess (响应结果) {String} highProductBaseModel.ageControlChannel *      年龄控制渠道 A控制全部 B只控制柜台 C只控制网站
     * @apiSuccess (响应结果) {Number} highProductBaseModel.minAppAmt 最低申请金额
     * @apiSuccess (响应结果) {Number} highProductBaseModel.minAppVol 最小申请份额
     * @apiSuccess (响应结果) {Number} highProductBaseModel.minSuppleAmt 最低追加金额
     * @apiSuccess (响应结果) {Number} highProductBaseModel.minSuppleVol 最小追加赎回份额
     * @apiSuccess (响应结果) {Number} highProductBaseModel.maxAppAmt 最大申请申请金额
     * @apiSuccess (响应结果) {Number} highProductBaseModel.maxAppVol 最大赎回份额
     * @apiSuccess (响应结果) {Number} highProductBaseModel.maxSumAmt 每日最大累计申请金额
     * @apiSuccess (响应结果) {Number} highProductBaseModel.maxSumVol 每日最大累计申请份额
     * @apiSuccess (响应结果) {String} highProductBaseModel.fixedIncomeFlag 底层固定收益标识0-否 1-是
     * @apiSuccess (响应结果) {Number} highProductBaseModel.minRetainedAsset 最底保留资产
     * @apiSuccess (响应结果) {String} highProductBaseModel.hasAchievementPayFlag 是否业绩计提标识0-否 1-是
     * @apiSuccess (响应结果) {String} highProductBaseModel.gaoYiLingShanFlag 是否高毅领山产品 0-否 1-是
     * @apiSuccess (响应结果) {Array} fundFeeRateList 高端产品费率信息
     * @apiSuccess (响应结果) {String} fundFeeRateList.fundCode
     * @apiSuccess (响应结果) {String} fundFeeRateList.shareClass
     * @apiSuccess (响应结果) {String} fundFeeRateList.invstType
     * @apiSuccess (响应结果) {String} fundFeeRateList.getFeeRateMethod
     * @apiSuccess (响应结果) {Number} fundFeeRateList.minFeeVol
     * @apiSuccess (响应结果) {Number} fundFeeRateList.maxFeeVol
     * @apiSuccess (响应结果) {Number} fundFeeRateList.minFeeAmt
     * @apiSuccess (响应结果) {Number} fundFeeRateList.maxFeeAmt
     * @apiSuccess (响应结果) {Number} fundFeeRateList.minFeeDays
     * @apiSuccess (响应结果) {Number} fundFeeRateList.maxFeeDays
     * @apiSuccess (响应结果) {String} fundFeeRateList.busiCode
     * @apiSuccess (响应结果) {String} fundFeeRateList.captType
     * @apiSuccess (响应结果) {String} fundFeeRateList.tfundCode
     * @apiSuccess (响应结果) {String} fundFeeRateList.tshareClass
     * @apiSuccess (响应结果) {Number} fundFeeRateList.constantFee
     * @apiSuccess (响应结果) {Number} fundFeeRateList.maxFee
     * @apiSuccess (响应结果) {Number} fundFeeRateList.minFee
     * @apiSuccess (响应结果) {Number} fundFeeRateList.feeRate
     * @apiSuccess (响应结果) {String} fundFeeRateList.feeRateFlag
     * @apiSuccess (响应结果) {String} fundFeeRateList.startDt
     * @apiSuccess (响应结果) {String} fundFeeRateList.endDt
     * @apiSuccess (响应结果) {String} fundFeeRateList.returnCode
     * @apiSuccess (响应结果) {Number} agendDisc 代销折扣
     * @apiSuccess (响应结果) {Array} highProductActiDiscountModelList 高端产品活动折扣信息
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.fundCode
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.fundType
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.disCode
     * @apiSuccess (响应结果) {Number} highProductActiDiscountModelList.discountRatio
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.discountType
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.discountRate
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.startDt
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.endDt
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.startTm
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.endTm
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.paySource
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.bankCode
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.actiNo
     * @apiSuccess (响应结果) {Number} activityRate 活动折扣
     * @apiSuccess (响应结果) {String} matchRiskLevelFlag 风险评估是否匹配 0-否 1-是 2-风险已过期
     * @apiSuccess (响应结果) {Boolean} testForceMatchFlag 不匹配是否强制不能购买 true:强制风险匹配 false:不强制风险匹配
     * @apiSuccess (响应结果) {Object} subsciptionVO crm预约信息
     * @apiSuccess (响应结果) {String} subsciptionVO.id 预约单标识
     * @apiSuccess (响应结果) {Number} subsciptionVO.bookAmount 预约金额
     * @apiSuccess (响应结果) {Number} subsciptionVO.sellVol 赎回金额
     * @apiSuccess (响应结果) {Number} subsciptionVO.bookRatio 折扣率
     * @apiSuccess (响应结果) {Number} subsciptionVO.baseFeeRate 基本费率
     * @apiSuccess (响应结果) {String} subsciptionVO.jjjc 基金简称
     * @apiSuccess (响应结果) {String} subsciptionVO.bankCode 默认银行卡
     * @apiSuccess (响应结果) {String} subsciptionVO.bankProv 默认开户省份
     * @apiSuccess (响应结果) {String} subsciptionVO.bankCity 默认开户城市
     * @apiSuccess (响应结果) {String} subsciptionVO.bankNo 默认银行卡号
     * @apiSuccess (响应结果) {String} subsciptionVO.preIdType 预约id类型
     * @apiSuccess (响应结果) {String} subsciptionVO.discountUseType 折扣类型：1-使用折扣率，2-使用折扣金额      {@link com.howbuy.crm.base.discount.DisCountUseTypeEnum}
     * @apiSuccess (响应结果) {Number} subsciptionVO.discountAmt 折扣金额
     * @apiSuccess (响应结果) {Number} surplusAmount 产品剩余额度
     * @apiSuccess (响应结果) {String} hbOneMobileBind 是否不绑定一帐通手机号 0-否 1-是
     * @apiSuccess (响应结果) {String} validEmailBind 校验邮箱是否绑定
     * @apiSuccessExample 响应结果示例
     * {"fundFeeRateList":[{"minFeeVol":610.*************,"maxFeeVol":5761.************,"maxFeeDays":782,"shareClass":"DsTEQefOOQ","captType":"ViRPTJ","busiCode":"tHxC5","minFeeAmt":1447.*************,"endDt":"va201Hg","feeRateFlag":"JgG","maxFee":3671.*************,"tshareClass":"sYQ","feeRate":1625.********56838,"minFee":788.9170861863048,"tfundCode":"Y","getFeeRateMethod":"j0kFuUvBG","returnCode":"BXUSwDHSL","maxFeeAmt":8263.92799552191,"fundCode":"euNj","invstType":"0ajAGAor","constantFee":1765.5001947684034,"startDt":"I","minFeeDays":6342}],"validEmailBind":"3iR","matchRiskLevelFlag":"Ydwodx","kycResponse":{"riskToleranceLevel":"LPz9s2o9J","investorType":"9eD"},"payEndDate":"k","highProductBaseModel":{"directOpenFlag":"e","prodDiffer":4818,"productChannel":"TH","purConfirmDays":3858,"shareClass":"L","shareCtrlDate":"5AbupwuiK9","redeemMemo":"f3fhZ","supplyAgreementUrlList":[{"id":"kE7Il","url":"p7"}],"calmTime":497,"fundAttr":"GfhL","fundClass":"vue","dfltDivMode":"xt96biOE","isSupSelfDrawing":"u2q3","taCode":"8RWaFAcN","fundCode":"XmQbrS0g","supportAdvanceFlag":"VjbDjONCY","ageControlFlag":"tu","eContract":"2MMIg21q","isSupCounter":"YMzg6z88hC","ipoStartDt":"btKojU","totalPlaces":1680,"productClass":"4baTiv","totalBalance":6169.644018396436,"redeOpenTerm":"6GHM","maxSumAmt":5794.898558129716,"fundOpenMode":"sQph","minSuppleAmt":2992.045736739092,"riskNoticeUrl":"qJcxCbIdwC","maxSumVol":7469.335684401774,"minAcctVol":6196.595768327967,"distributeSize":4380.482441405592,"fundRiskLevel":"WXudr9U","openFlag":"zB0ELCbH","gaoYiLingShanFlag":"2hO","prodRedmDays":6762,"holidayStrategy":"YEqAiWQc8","minAge":4926,"openDt":"J3","minRetainedAsset":3602.76708277239,"hasAchievementPayFlag":"9s","fundName":"lX33ZoH","fundAttrPinyin":"NmUm1Sp","maxAppAmt":3993.316598175819,"netMinAppAmt":4907.7944502054515,"redeConfirmDays":8344,"confirmDays":4841,"testForceMatchFlag":"5fpgZ5p2t","redeemDirectionIsSupCxgFlag":"GI2rYokLa","structureFlag":"8VDAu","fundWebUrl":"gZmYN","fundType":"kG","multiCardFlag":"sCQ","establishDt":"7YO","minAppVol":5303.340197729729,"isSupPiggy":"8rJuB","minSuppleVol":6746.************,"prodSubDays":1371,"ageControlChannel":"2J","saleAgreementUrl":"v1e5M","mainFundCode":"RCX","prodRollRange":2552,"feeCalMode":"TAo2G7","supplyAgreementUrl2":"zsLT","maxAppVol":4627.************,"supplyAgreementUrl3":"8kkizzMZlB","supplyAgreementUrl1":"R9","supplyAgreementUrl6":"nH","supplyAgreementUrl4":"H","supplyAgreementUrl5":"C84WUYPDj","fixedIncomeFlag":"VvPKJPFb","redePaymentDays":2046,"elecContractUrl":"VG5YTK97","redeemDirectionIsSupCardFlag":"RaKJ4DG","paymentBankChangeUrl":"5ZUmM8s0h0","minAppAmt":5478.************,"sacFundCode":"Ci5","isSupAgentDrawing":"yy37IaHS","fundSubType":"L","taName":"DMS","maxAge":3258,"endTm":"UBggjl4FP","redeemType":"Bo3g","divMethodFlag":"Q","suppleSubsRule":"CDs","ipoEndDt":"3P9l","netMinSuppleAmt":196.**************,"isSupWeb":"7deUzZ3i"},"productChannel":"7sC","shareClass":"jPTQ","openStartDt":"Q","highProductActiDiscountModelList":[{"discountRate":"gJKzElO","bankCode":"pxSCWT7mj","endDt":"SRlpIrQZv1","paySource":"w3Qb4EByp","disCode":"B","fundType":"6Zg1Qiaja","startTm":"3mUQcY3ES3","fundCode":"S6DfLgZ","endTm":"1N6Na7tl3","actiNo":"ppXA","startDt":"pth","discountType":"Z","discountRatio":6020.************}],"testForceMatchFlag":false,"hbOneMobileBind":"doXv","isBought":"qDmmkgP","appointmentStartDt":"Oq9","appointmentEndDt":"CF","subsciptionVO":{"bankCode":"McIqalHwmI","bookRatio":5197.************,"bookAmount":192.**************,"jjjc":"1Q5g","bankNo":"AEf3PnB","id":"WZXJBf","sellVol":2349.*************,"bankProv":"ITL9","baseFeeRate":155.**************,"preIdType":"C","bankCity":"8m7"},"agendDisc":3217.*************,"openEndDt":"pRaCCLAki","activityRate":6400.***********,"surplusAmount":4420.************,"isHasNopayAmt":"5wjmnR"}
     */
    @RequestMapping("/simu/trade/buy.htm")
    public void buy(HttpServletRequest request, HttpServletResponse response) throws IOException {
        TradeSession loginInfo = this.getCustSession();

        // 一帐通绑定手机号
        String hboneBindMobile = getHboneBindMobileMask(loginInfo.getUser().getHboneNo());
        // 获取请求参数对象
        BuyCmd buyCmd = getCommand(BuyCmd.class);
        ValidateUtil.assertValid(buyCmd);
        String fundCode = buyCmd.getFundCode();
        //客户类型 0-机构用户 1-个人用户 默认1-个人用户
        String investType = StringUtils.isNotBlank(loginInfo.getUser().getInvstType()) ? loginInfo.getUser().getInvstType() : "1";

        // 查询产品基本信息
        HighProductInfoModel highProductBaseModel = buyService.getHighProductInfo(fundCode);

        //查询kyc信息
        KycInfoResponse kycInfoResponse = buyService.getKycInfo(loginInfo.getUser().getHboneNo(), DisCodeEnum.HM.getCode());

        // 验证kyc信息: 资管合格投资确认书是否签署
        buyService.valdiateQualifyKycInfo(kycInfoResponse, highProductBaseModel);

        //产品购买状态校验
        buyService.validProductBuyStatus(request, fundCode, loginInfo.getUser().getCustNo());

        //是否追加逻辑
        QuerySuppleStatusResponse querySuppleStatusResponse = getIsBought(loginInfo.getUser().getCustNo(), fundCode, highProductBaseModel.getShareClass());

        //查询购买预约开放日历
        HighProductAppointmentInfoModel buyAppointmentInfoModel = highProductService.getAppointmentInfoByAppointDate(fundCode, "0",
                highProductBaseModel.getShareClass(), DisCodeEnum.HM.getCode(), new Date());

        // 业务代码
        String busiCode = buyService.getBusiCode(highProductBaseModel);
        log.info("buy|fundCode:{}, busiCode:{}", fundCode, busiCode);
        // 费率
        List<HighProductFeeRateModel> feeList = highProductService.getHighProductFeeRate(fundCode, investType, busiCode, highProductBaseModel.getShareClass());
        //活动费率
        HighProductActiDiscountListModel model = buyService.getActiDiscount(fundCode, highProductBaseModel.getShareClass(), busiCode);

        //查询产品限额信息
        HighProductLimitModel highFundLimitModel = highProductService.getHighProductLimitInfo(fundCode, highProductBaseModel.getShareClass(), busiCode, investType);

        //获取可使用预约信息，放入缓存
        PrebookDetailDto prebookDetailDto = buyService.getCurrentPreInfo(request, loginInfo, fundCode);

        // 获取用户可用剩余额度
        BigDecimal leftAmt = getCustLeftAmt(loginInfo.getUser().getTxAcctNo(), fundCode);

        // 构建返回参数
        BuyDto dto = buildBuyDto(kycInfoResponse, querySuppleStatusResponse, buyAppointmentInfoModel, highProductBaseModel, highFundLimitModel, feeList, model,
                prebookDetailDto, leftAmt, hboneBindMobile);

        write(dto, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }


    /**
     * @api {GET} /simu/trade/hzBuyPreCheck.htm hzBuyPreCheck
     * @apiVersion 1.0.0
     * @apiGroup BuyController
     * @apiName hzBuyPreCheck
     * @apiDescription 好臻下单前校验
     * @apiParam (请求参数) {String} fundCode 基金代码
     * @apiParamExample 请求参数示例
     * tokenId=US7&corpId=T5UIcqlQI&fundCode=2u29AuyHvW&operIp=C9zhEV&actionId=4rDaZ&disCode=FBnsk13Fk&outletCode=e
     * @apiSuccess (响应结果) {Object} accountStatusInfo 用户账户校验信息
     * @apiSuccess (响应结果) {String} accountStatusInfo.hasHzAccount 是否有好臻账户,1:是;0:否
     * @apiSuccess (响应结果) {String} accountStatusInfo.hasHzAccountActive 好臻账户是否已激活,1:是;0:否
     * @apiSuccess (响应结果) {String} accountStatusInfo.hasHmAccount 是否有好买账户,1:是;0:否
     * @apiSuccess (响应结果) {String} accountStatusInfo.hasHmAccountActive 好买账户是否已激活,1:是;0:否
     * @apiSuccess (响应结果) {String} isComply 合规校验是否通过,1:是;0:否
     * @apiSuccess (响应结果) {String} hzIdCardUploaded 当前客户好臻证件是否上传,1:是;0:否
     * @apiSuccess (响应结果) {String} hzAntiFraudPassed 好臻反洗钱要素是否校验通过,1:是;0:否
     * @apiSuccess (响应结果) {Object} fundRiskInfo 产品风险信息
     * @apiSuccess (响应结果) {String} fundRiskInfo.hzRiskAssessmentPassed 好臻风险测评是否通过,1:是;0:否
     * @apiSuccess (响应结果) {String} fundRiskInfo.currentRiskLevel 当前用户风险等级
     * @apiSuccess (响应结果) {String} fundRiskInfo.fundRiskLevel 产品风险等级
     * @apiSuccess (响应结果) {String} hasHzBankCard 好臻账户是否有绑定的银行卡,0:没有,1:有
     * @apiSuccess (响应结果) {String} haOnWayTrade 是否有在途交易,1:是;0:否
     * @apiSuccess (响应结果) {Object} complianceStateDto 合规状态信息
     * @apiSuccess (响应结果) {String} complianceStateDto.elecSignFlag 电子签名约定书签署标识 0-未签；1-已签
     * @apiSuccess (响应结果) {String} complianceStateDto.signFlag 私募合格投资者承诺书签署状态： 0-未签署；1-已签署
     * @apiSuccess (响应结果) {String} complianceStateDto.riskFlag 客户风险评测状态 0-未评测；1-已评测未过期；2-已过期
     * @apiSuccess (响应结果) {String} complianceStateDto.riskLevel 客户风险等级 0,1,2,3,4,5 没有做过的情况下为null
     * @apiSuccess (响应结果) {String} complianceStateDto.investorType 投资者类型,专业:PRO;普通:NORMAL
     * @apiSuccessExample 响应结果示例
     * {"hzIdCardUploaded":"UlWaBcjGU","hasHzBankCard":"zYWUuV91ml","hzAntiFraudPassed":"Ez6","fundRiskInfo":{"fundRiskLevel":"LAshBM3eK","currentRiskLevel":"1Inf","hzRiskAssessmentPassed":"eyp5TbLMWA"},"complianceStateDto":{"signFlag":"so","riskLevel":"b","elecSignFlag":"6pOF2G","riskFlag":"m","investorType":"DLlmXFzw"},"accountStatusInfo":{"hasHmAccount":"x3UgrzS8","hasHzAccountActive":"XpVQGyMj","hasHmAccountActive":"WdlvBk","hasHzAccount":"k"},"isComply":"oXF792q","haOnWayTrade":"H"}
     */
    @RequestMapping("/simu/trade/hzBuyPreCheck.htm")
    public void hzBuyPreCheck(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String fundCode = getString("fundCode");
        TradeSession loginInfo = this.getCustSession();
        HzBuyCheckResultVo hzBuyCheckResultVo = buyService.hzBuyPreCheck(fundCode, loginInfo, WebUtil.getCustIP(request));
        write(hzBuyCheckResultVo, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * @api {GET} /simu/trade/hmBuyPreCheck.htm hmBuyPreCheck
     * @apiVersion 1.0.0
     * @apiGroup BuyController
     * @apiName hmBuyPreCheck
     * @apiDescription 好买下单前置校验
     * @apiParam (请求参数) {String} fundCode 基金代码
     * @apiParamExample 请求参数示例
     * tokenId=US7&corpId=T5UIcqlQI&fundCode=PE0032&operIp=127.0.0.1
     * @apiSuccess (响应结果) {String} idCardIsExpired 身份证有效期是否过期,1:过期,0:未过期
     * @apiSuccess (响应结果) {String} submitTaDt 预计上报日,yyyyMMdd
     * @apiSuccess (响应结果) {String} isTaForceControl 是否ta强控,1:是,0:否
     * @apiSuccess (响应结果) {String} hmAntiFraudPassed 好买反洗钱要素是否校验通过,1:是;0:否
     * @apiSuccessExample 响应结果示例
     * {"submitTaDt":"20221102","isTaForceControl":"0","idCardIsExpired":"1","hmAntiFraudPassed":"1"}
     */
    @RequestMapping("/simu/trade/hmBuyPreCheck.htm")
    public void hmBuyPreCheck(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String fundCode = getString("fundCode");
        if (StringUtils.isBlank(fundCode)) {
            throw new ValidateException(ExceptionCodes.PARAMS_ERROR, "产品编码不能为空");
        }
        TradeSession loginInfo = this.getCustSession();
        HmBuyCheckResultVo hmBuyCheckResultVo = buyService.hmBuyPreCheck(fundCode, loginInfo);
        write(hmBuyCheckResultVo, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * @api {GET} /simu/trade/hzBuyFirstPage.htm hzBuyFirstPage
     * @apiVersion 1.0.0
     * @apiGroup BuyController
     * @apiName hzBuyFirstPage
     * @apiDescription 下单第一页
     * @apiParam (请求参数) {String} fundCode 基金代码
     * @apiParam (请求参数) {String} tokenId 客户端token
     * @apiParam (请求参数) {String} corpId 商户号
     * @apiParam (请求参数) {String} actionId 活动号
     * @apiParam (请求参数) {String} operIp IP
     * @apiParam (请求参数) {String} disCode 分销代码
     * @apiParam (请求参数) {String} outletCode 分销网点号
     * @apiParamExample 请求参数示例
     * tokenId=gx&corpId=sxPgRFew8&fundCode=dt2o&operIp=4u&actionId=1loRV&disCode=vQJLC4O5&outletCode=Rd8segyJ
     * @apiSuccess (响应结果) {Object} hzBuyFirstFundInfoDto 产品首单信息
     * @apiSuccess (响应结果) {String} hzBuyFirstFundInfoDto.openStartDt 开放起始日
     * @apiSuccess (响应结果) {String} hzBuyFirstFundInfoDto.payEndDate 打款截止日
     * @apiSuccess (响应结果) {Number} hzBuyFirstFundInfoDto.netAmtLowerLimit 净金额下限
     * @apiSuccess (响应结果) {Number} hzBuyFirstFundInfoDto.levelDiff 级差
     * @apiSuccess (响应结果) {String} hzBuyFirstFundInfoDto.peDivideCallFlag 是否分次CALL款股权产品 0-否 1是
     * @apiSuccess (响应结果) {Number} hzBuyFirstFundInfoDto.payRatio 本次实缴比例
     * @apiSuccess (响应结果) {String} hzBuyFirstFundInfoDto.feeCalMode 费用计算方式,0-外扣法；1-内扣法
     * @apiSuccess (响应结果) {Object} hzOrderBuyAmtInfoDto 好臻订单购买金额相关信息实体
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.subscribeAmt 认缴金额(只有分次call才有)
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.paidAmt 实缴金额
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.buyAmt 购买金额(购买金额只有非分次call才有)
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.buyAmtCanUpdate 购买金额是否可以修改,1:可以;0:不可以
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.needCheckAmt 是否需要校验限额,1:需要;0:不需要
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.subscribeAmtCanUpdate 认缴金额是否可以修改,1:可以;0:不可以
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.isFirstPay 是否首次实缴,1:是;0:不是
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.bankCardCanUpdate 银行卡号是否可以修改,1:可以;0:不可以
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.feeRateMethod 计算手续费方式,4:按认缴金额,5:按实缴金额
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.confirmSubscribeAmt 认缴表中的认缴金额(只有分次call才有)
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.confirmPaidAmt 已实缴金额
     * @apiSuccess (响应结果) {Array} hzOrderBuyAmtInfoDto.customerBankCardInfoList 银行卡号信息
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.customerBankCardInfoList.bankCode 银行编号
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.customerBankCardInfoList.bankAcctMask 银行卡号掩码
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.customerBankCardInfoList.bankRegionName 分行名称
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.customerBankCardInfoList.bankName 银行名称
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.customerBankCardInfoList.bankLogoUrl 银行图标地址
     * @apiSuccess (响应结果) {Object} hzOrderBuyAmtInfoDto.highProductBaseModel 产品信息
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.branchCode 购买渠道 1-柜台 2-非柜台 3-全部
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.directOpenFlag 是否对特定客户开放 0-否 1-是
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.visitForceFlag 上报是否需要强制回访 0-否 1-是
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.highProductBaseModel.calmTime 冷静期时间
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.calmOverFlag 上报是否需要过冷静期 0-否 1-是
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.assetProvFlag 上报是否需要资产证明 0-否 1-是
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.partRedeemType 部分赎回确认方式 1部分确认，保留最低保留资产 2全部成交 3全部失败 4按申请份额确认
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.redeemMemo 赎回备注
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.highProductBaseModel.maxAge 年龄上限
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.highProductBaseModel.minAge 年龄下限
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.ageControlFlag 是否控制 0-否 1-是
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.ageControlChannel 控制渠道 0-全部 1-柜台 2-非柜台
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.paymentTypeList 支付通道 111每位分别代表储蓄罐,自划款,代扣
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.redeemDirectionList 赎回去向 11每位分别代表储蓄罐银行卡
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.fundInvestAdvise 投资建议
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.fundWebUrl 网页链接
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.highProductBaseModel.largeRedeemRate 巨额赎回比例
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.taStat TA状态: 0-正常；1-关闭；2-暂停
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.supportCardType 是否支持多卡 1-多卡 2-产品单卡 3-TA单卡
     * @apiSuccess (响应结果) {Array} hzOrderBuyAmtInfoDto.highProductBaseModel.agreementConfList 协议list
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.isTrust 是否信托产品0-否 1-是
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.forceMatchRiskFlag 监管需要风险强制匹配
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.fixedIncomeFlag 底层固定收益标识0-否 1-是
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.onlySuppleFlag 是否只支持追加 0-否 1-是
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.hasAchievementPayFlag 是否业绩计提 0-否 1-是
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.peDivideCallFlag 是否分次CALL款股权产品 0-否 1是
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.gaoYiLingShanFlag 是否高毅领山产品 0-否 1-是
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.buyUserType 可购买客户类型，逗号分隔多个 0-全部 1-个人，2-机构，3-产品
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.disCode 分销代码
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.autoContractFlag 自动生成电子合同 0-否 1-是
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.submitSpecialFlag 上报特殊处理 0-否 1-是
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.fundCode 基金代码
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.fundName 基金名称
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.disCode 分销渠道
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.fundAttr 基金简称
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.fundAttrPinyin 基金简称拼音
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.taCode TA代码
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.fundType 基金类型 7-一对多专户 11-私募
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.fundSubType 基金子类型 0-股票型 1-混合型 2-债券型 3-货币型 4-QDII 5-封闭式 6-结构型 7-一对多专户 8-券商大集合 9-券商小集合      A-固定收益 B-对冲型 C-PE/VC D-其他 10-FOF'
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.dfltDivMode 基金默认分红方式
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.divMethodFlag 是否允许修改分红方式 0-不允许 1-允许
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.fundRiskLevel 基金风险等级；1-低风险；2-中低风险；3-中风险；4-中高风险；5-高风险
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.ipoStartDt 募集开始日期
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.ipoEndDt 募集结束日期
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.establishDt 成立日期
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.fundEndDt 产品结束日期
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.openFlag 开通标志 0-未开通;1-已开通
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.highProductBaseModel.prodDiffer 首次购买级差
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.isScheduledTrade 支持预约交易标识，0-不支持；1-仅支持购买预约；2-仅支持赎回预约；3-支持购买赎回预约
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.fundManCode 基金管理人代码
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.currency 币种
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.highProductBaseModel.faceValue 基金面值
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.feeCalMode 费用计算方式,0-外扣法；1-内扣法
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.fundCustodianCode 基金托管人代码
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.hasLockPeriod 0-否,1-是
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.mainFundCode 主基金代码
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.taFundCode TA产品代码
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.asocFundCode 协会产品代码
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.isMotherFund 是否母基金
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.highProductBaseModel.navPrecision 净值精度
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.highProductBaseModel.volPrecision 份额精度
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.highProductBaseModel.minAcctVol 最低持有份额
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.highProductBaseModel.minRetainedAsset 最低保留资产
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.highProductBaseModel.ipoAmountLimit 募集金额上限
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.highProductBaseModel.ipoPeopleLimit 募集人数上限
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.highProductBaseModel.redePaymentDays 赎回到账天数
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.structureFlag 是否结构化基金 0-否 1-是
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.productChannel 产品通道1 好买创新、2 创昱达、3 群济、4 好买储蓄罐、5 好买公募、6 高端公募、7 TP私募
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.productClass 产品类别：1-组合；2-零售；3-高端；4-定期
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.shareClass 份额类型，A-前收费；B-后收费
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.recStat 记录状态,0-有效；1-无效
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.isBook 是否支持提前下单0-否 1-是
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.appointStartDt 预约开始日期
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.apponitEndDt 预约截止日期
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.openStartDt 开放开始日期
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.openEndDt 开放截止日期
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.payDeadlineDtm 打款截止日期
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.highProductBaseModel.investMinAmount 投资起点
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.productSaleType 产品销售类型 0-好买 1-海外 2-其他
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.managerAttribute 管理人属性 1-其他；2-私募；3-资管
     * @apiSuccess (响应结果) {String} hzOrderBuyAmtInfoDto.highProductBaseModel.fundManName 基金管理人名称
     * @apiSuccess (响应结果) {Number} hzOrderBuyAmtInfoDto.highProductBaseModel.prodAppDiffer 首次购买产品级差
     * @apiSuccess (响应结果) {String} appointmentDealNo 预约单信息
     * @apiSuccessExample 响应结果示例
     * {"appointmentDealNo":"I0fj","hzBuyFirstFundInfoDto":{"payEndDate":"0If","openStartDt":"1d8L","payRatio":1111.9794623929301,"levelDiff":6963,"peDivideCallFlag":"Ywa8Q","feeCalMode":"uNAb32b","netAmtLowerLimit":732.0976578897287},"hzOrderBuyAmtInfoDto":{"buyAmt":9854.559265864355,"highProductBaseModel":{"directOpenFlag":"XWlT3nUZz","prodDiffer":9868,"asocFundCode":"MMDd","productChannel":"PR","ipoAmountLimit":114.66794235585832,"shareClass":"YuKB","fundManName":"u","calmOverFlag":"1","taFundCode":"04","redeemMemo":"ZZn","disCode":"1","submitSpecialFlag":"rNcQ0k","calmTime":7680,"agreementConfList":[],"fundAttr":"WI","taStat":"yMHp","dfltDivMode":"zo","isBook":"UU3","investMinAmount":7556.616135187213,"taCode":"7zNe","fundCode":"jziaHqElib","productSaleType":"IPM","ageControlFlag":"1cs60Xs1EW","volPrecision":3065,"openEndDt":"zS2NKz","assetProvFlag":"aVDpzFWE","ipoStartDt":"eF","isScheduledTrade":"BM2W","productClass":"JC8Pj","buyUserType":"m1PpOdAw","peDivideCallFlag":"6FUs65h","minAcctVol":5572.322755563955,"branchCode":"VMeXJcM5oS","fundCustodianCode":"Fgdg","fundRiskLevel":"iPcBC","gaoYiLingShanFlag":"ihk","openFlag":"mLUg","visitForceFlag":"hcD8un","minAge":8688,"fundEndDt":"XVtuWe","minRetainedAsset":3432.0262270571934,"hasAchievementPayFlag":"Hgns","prodAppDiffer":2552,"fundName":"MTOX7zE5r","managerAttribute":"zcU","fundAttrPinyin":"Xxn8GUU","openStartDt":"GYgWg","structureFlag":"gIioCSxa","fundWebUrl":"VPe6M","isTrust":"mLS","largeRedeemRate":2207.9460427186705,"fundType":"fhLkKJ","establishDt":"MJRD","hasLockPeriod":"5Lj0GwZMMk","payDeadlineDtm":"t5lA3","ageControlChannel":"c5","mainFundCode":"guVpogM","navPrecision":4380,"currency":"v65pvo4","redeemDirectionList":"AvCTM19d","feeCalMode":"lJ459IJpaP","isMotherFund":"Ddxmn1h2","forceMatchRiskFlag":"THi7Ati","appointStartDt":"fDsNox5c2G","onlySuppleFlag":"kjO6OEH","fundManCode":"nKfNCS4qbp","fixedIncomeFlag":"nMwGZ1GtfU","redePaymentDays":4807,"fundInvestAdvise":"Jo","autoContractFlag":"en","ipoPeopleLimit":1899,"apponitEndDt":"qBE","supportCardType":"gjNP5I","fundSubType":"gy7NxNsm","partRedeemType":"bOC25GaO","maxAge":688,"paymentTypeList":"uBzY","faceValue":4979.************,"divMethodFlag":"QSR","ipoEndDt":"UoJ","recStat":"rkZCl"},"feeRateMethod":"ASwFRr","subscribeAmtCanUpdate":"KzRKs6g20","paidAmt":7189.***********,"confirmPaidAmt":1387.*************,"buyAmtCanUpdate":"kB","needCheckAmt":"Wd6L0DhG","isFirstPay":"v","subscribeAmt":3039.************,"bankCardCanUpdate":"IOaPJZYyEq","customerBankCardInfoList":[{"bankLogoUrl":"sUe7","bankCode":"oqfdXDQkAx","bankAcctMask":"j4LAkKywX","bankRegionName":"6cZSNrs3X0","bankName":"W"}],"confirmSubscribeAmt":2141.************}}
     */
    @RequestMapping("/simu/trade/hzBuyFirstPage.htm")
    public void hzBuyFirstPage(HttpServletRequest request, HttpServletResponse response) throws IOException {
        TradeSession loginInfo = this.getCustSession();
        // 获取请求参数对象
        BuyCmd buyCmd = getCommand(BuyCmd.class);
        ValidateUtil.assertValid(buyCmd);
        buyCmd.setDisCode(DisCodeEnum.HZ.getCode());
        HzBuyFirstPageVo hzBuyFirstPageInfo = buyService.getHzBuyFirstPageInfo(request, buyCmd, loginInfo);
        write(hzBuyFirstPageInfo, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * @api {GET} /simu/trade/serviceEntityPopInfo.htm serviceEntityPopInfo
     * @apiVersion 1.0.0
     * @apiGroup BuyController
     * @apiName serviceEntityPopInfo
     * @apiDescription 服务主体弹窗
     * @apiSuccess (响应结果) {String} fpqcCode 基金从业资格证编码
     * @apiSuccess (响应结果) {String} title 标题
     * @apiSuccess (响应结果) {String} content 文案
     * @apiSuccess (响应结果) {String} headerUrl 头像
     * @apiSuccess (响应结果) {String} name 投顾姓名
     * @apiSuccess (响应结果) {String} rank 投顾职级
     * @apiSuccess (响应结果) {String} mobile 手机号
     * @apiSuccess (响应结果) {String} occupationCode 理财师职业编号
     * @apiSuccessExample 响应结果示例
     * {"occupationCode":"hLQPjmQC","name":"V8wOTt","mobile":"5DmBWy","rank":"okADqlq","FpqcCode":"wQRTE","headerUrl":"Lp2Yp","title":"7lH","content":"Nl8kJ5Zvx"}
     */
    @RequestMapping("/simu/trade/serviceEntityPopInfo.htm")
    public void serviceEntityPopInfo(HttpServletRequest request, HttpServletResponse response) throws IOException {
        TradeSession loginInfo = this.getCustSession();
        ServiceEntityPopInfoVo serviceEntityPopInfoVo = buyService.queryServiceEntityPopInfo(loginInfo);
        write(serviceEntityPopInfoVo, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }


    /**
     * @api {GET} /simu/trade/queryFeeAmt.htm queryFeeAmt
     * @apiVersion 1.0.0
     * @apiGroup BuyController
     * @apiName queryFeeAmt
     * @apiDescription 手续费相关接口
     * @apiParam (请求参数) {String} fundCode 基金代码
     * @apiParam (请求参数) {Number} subsAmt 认缴金额
     * @apiParam (请求参数) {String} paymentType 支付方式：01-自划款；04-银行卡代扣；06-储蓄罐支付
     * @apiParam (请求参数) {String} isFirstPay 是否首次实缴,1:是;0:不是
     * @apiParam (请求参数) {String} appointmentDealNo 预约订单号
     * @apiParam (请求参数) {Number} discountRate OP手动输入的折扣
     * @apiParam (请求参数) {String} bankCode 银行编码
     * @apiParamExample 请求参数示例
     * discountRate=707.*************&bankCode=hT4lYKhQn&isFirstPay=IJn4AFfyvp&fundCode=WD&&appointmentDealNo=1F85&subsAmt=9872.************&paidAmt=4429.************&paymentType=jk
     * @apiSuccess (响应结果) {Number} fee 费用
     * @apiSuccess (响应结果) {Number} discountRate 最终折扣
     * @apiSuccess (响应结果) {Number} originalFee 原始费用
     * @apiSuccess (响应结果) {Number} actualPayAmt 实际支付金额
     * @apiSuccess (响应结果) {Number} paidAmt 实缴金额
     * @apiSuccess (响应结果) {Number} appointAmt 预约金额
     * @apiSuccess (响应结果) {Number} appointDiscount 预约折扣
     * @apiSuccess (响应结果) {String} disCountEffect 折扣是否生效,1:生效,0:不生效
     * @apiSuccess (响应结果) {String} paidAmtLessThanNeedPaidAmt 实缴金额是否小于需要支付金额,1:小于,0:大于等于
     * @apiSuccessExample 响应结果示例
     * {"discountRate":5147.************,"originalFee":6154.************,"appointDiscount":7293.***********6,"disCountEffect":"vx","fee":8029.421045837208,"paidAmtLessThanNeedPaidAmt":"r08Uk","actualPayAmt":3727.0103337717774,"paidAmt":4276.808416915597,"appointAmt":2344.709663193657}
     */
    @RequestMapping("/simu/trade/queryFeeAmt.htm")
    public void queryFeeAmt(HttpServletRequest request, HttpServletResponse response) throws IOException {
        TradeSession loginInfo = this.getCustSession();
        // 获取请求参数对象
        QueryFeeAmtCmd queryFeeAmtCmd = getCommand(QueryFeeAmtCmd.class);
        ValidateUtil.assertValid(queryFeeAmtCmd);
        BuyFeeInfoVo buyFeeInfoVo = buyService.queryFeeAmt(queryFeeAmtCmd, loginInfo, request);
        write(buyFeeInfoVo, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }


    private BigDecimal getCustLeftAmt(String txAcctNo, String fundCode) {
        String[] fundArr = {fundCode};
        // 查询剩余额度
        QueryProductQuotaRequest queryProductQuotaRequest = new QueryProductQuotaRequest();
        queryProductQuotaRequest.setTxAcctNo(txAcctNo);
        queryProductQuotaRequest.setProductCodeArr(fundArr);
        QueryProductQuotaResponse queryProductQuotaResponse = queryProductQuotaFacade.execute(queryProductQuotaRequest);
        if (queryProductQuotaResponse != null) {
            if (CollectionUtils.isNotEmpty(queryProductQuotaResponse.getQuotaBeanList())) {
                return queryProductQuotaResponse.getQuotaBeanList().get(0).getLeftAmt();
            }
        }

        return null;
    }


    /**
     * @api {POST} /simu/trade/buyConfirm.htm 购买确认（返回银行卡列表，储蓄罐份额列表，费率，活动折扣，代销折扣，预约折扣）
     * @apiVersion 1.0.0
     * @apiGroup buy
     * @apiName buyConfirm
     * @apiParam (请求参数) {String} fundCode 基金代码
     * @apiParam (请求参数) {String} tokenId 客户端token
     * @apiParam (请求参数) {String} corpId 商户号
     * @apiParam (请求参数) {String} actionId 活动号
     * @apiParam (请求参数) {String} operIp IP
     * @apiParam (请求参数) {String} disCode 分销代码
     * @apiParam (请求参数) {String} outletCode 分销网点号
     * @apiParamExample 请求参数示例
     * tokenId=V9r65&corpId=cye&fundCode=jY5ecR&operIp=hGB7&actionId=hNFDXCtimy&disCode=cRkv&outletCode=t
     * @apiSuccess (响应结果) {Boolean} showSavingBox 是否支持储蓄罐支付
     * @apiSuccess (响应结果) {String} isSupSelfDrawing 是否支持自划款 0-不支持 1-支持
     * @apiSuccess (响应结果) {String} isSupAgentDrawing 是否支持代扣0-不支持 1-支持
     * @apiSuccess (响应结果) {String} shareClass 收费类型 A-前收费 B-后收费
     * @apiSuccess (响应结果) {String} productChannel 产品通道 3-群济私募 5-好买公募
     * @apiSuccess (响应结果) {String} lastTimePayChannelCode 最近一次支付方式
     * @apiSuccess (响应结果) {String} lastTimeAccoundId 最近一次支付银行卡
     * @apiSuccess (响应结果) {Object} withdrawLimitDto 储蓄罐限额
     * @apiSuccess (响应结果) {String} withdrawLimitDto.subFlag
     * @apiSuccess (响应结果) {String} withdrawLimitDto.maxSeparationOrderCount
     * @apiSuccess (响应结果) {String} withdrawLimitDto.redeFlag
     * @apiSuccess (响应结果) {Number} withdrawLimitDto.balanceFastRedeAmt
     * @apiSuccess (响应结果) {String} withdrawLimitDto.quickRedeFlag
     * @apiSuccess (响应结果) {String} withdrawLimitDto.savingBoxFundStat
     * @apiSuccess (响应结果) {String} withdrawLimitDto.newProductState
     * @apiSuccess (响应结果) {Array} volDtlDtoList 储蓄罐份额列表
     * @apiSuccess (响应结果) {String} volDtlDtoList.fundCode
     * @apiSuccess (响应结果) {String} volDtlDtoList.fundAttr
     * @apiSuccess (响应结果) {Number} volDtlDtoList.fundInc
     * @apiSuccess (响应结果) {Number} volDtlDtoList.sevenYearRate
     * @apiSuccess (响应结果) {String} volDtlDtoList.fundNavDt
     * @apiSuccess (响应结果) {String} volDtlDtoList.custBankId
     * @apiSuccess (响应结果) {String} volDtlDtoList.bankCode
     * @apiSuccess (响应结果) {String} volDtlDtoList.bankLogoUrl
     * @apiSuccess (响应结果) {String} volDtlDtoList.bankName
     * @apiSuccess (响应结果) {String} volDtlDtoList.bankAcct
     * @apiSuccess (响应结果) {Number} volDtlDtoList.availAmt
     * @apiSuccess (响应结果) {Number} volDtlDtoList.totalIncome
     * @apiSuccess (响应结果) {Number} volDtlDtoList.unCarryOverIncome
     * @apiSuccess (响应结果) {Number} volDtlDtoList.totalAmt
     * @apiSuccess (响应结果) {String} volDtlDtoList.limitPerTime
     * @apiSuccess (响应结果) {String} volDtlDtoList.limitPerDay
     * @apiSuccess (响应结果) {Number} volDtlDtoList.depositUnackAmt
     * @apiSuccess (响应结果) {Number} volDtlDtoList.availRegAmt
     * @apiSuccess (响应结果) {String} volDtlDtoList.tradeFlag
     * @apiSuccess (响应结果) {String} volDtlDtoList.paySign
     * @apiSuccess (响应结果) {String} volDtlDtoList.spNumber
     * @apiSuccess (响应结果) {Number} volDtlDtoList.reserveFrzVol
     * @apiSuccess (响应结果) {String} volDtlDtoList.upgradeFlag
     * @apiSuccess (响应结果) {String} volDtlDtoList.downFundCode
     * @apiSuccess (响应结果) {String} volDtlDtoList.downFundName
     * @apiSuccess (响应结果) {Number} volDtlDtoList.upgradeLimit
     * @apiSuccess (响应结果) {String} volDtlDtoList.canRedeemFlag
     * @apiSuccess (响应结果) {Array} fundFeeRateList 基础费率列表
     * @apiSuccess (响应结果) {String} fundFeeRateList.fundCode
     * @apiSuccess (响应结果) {String} fundFeeRateList.shareClass
     * @apiSuccess (响应结果) {String} fundFeeRateList.invstType
     * @apiSuccess (响应结果) {String} fundFeeRateList.getFeeRateMethod
     * @apiSuccess (响应结果) {Number} fundFeeRateList.minFeeVol
     * @apiSuccess (响应结果) {Number} fundFeeRateList.maxFeeVol
     * @apiSuccess (响应结果) {Number} fundFeeRateList.minFeeAmt
     * @apiSuccess (响应结果) {Number} fundFeeRateList.maxFeeAmt
     * @apiSuccess (响应结果) {Number} fundFeeRateList.minFeeDays
     * @apiSuccess (响应结果) {Number} fundFeeRateList.maxFeeDays
     * @apiSuccess (响应结果) {String} fundFeeRateList.busiCode
     * @apiSuccess (响应结果) {String} fundFeeRateList.captType
     * @apiSuccess (响应结果) {String} fundFeeRateList.tfundCode
     * @apiSuccess (响应结果) {String} fundFeeRateList.tshareClass
     * @apiSuccess (响应结果) {Number} fundFeeRateList.constantFee
     * @apiSuccess (响应结果) {Number} fundFeeRateList.maxFee
     * @apiSuccess (响应结果) {Number} fundFeeRateList.minFee
     * @apiSuccess (响应结果) {Number} fundFeeRateList.feeRate
     * @apiSuccess (响应结果) {String} fundFeeRateList.feeRateFlag
     * @apiSuccess (响应结果) {String} fundFeeRateList.startDt
     * @apiSuccess (响应结果) {String} fundFeeRateList.endDt
     * @apiSuccess (响应结果) {String} fundFeeRateList.returnCode
     * @apiSuccess (响应结果) {Array} bankAccountList 用户绑定银行卡列表
     * @apiSuccess (响应结果) {String} bankAccountList.custBankId
     * @apiSuccess (响应结果) {String} bankAccountList.bankAcct
     * @apiSuccess (响应结果) {String} bankAccountList.bankAcctDigest
     * @apiSuccess (响应结果) {String} bankAccountList.bankAcctMask
     * @apiSuccess (响应结果) {String} bankAccountList.bankAcctFull
     * @apiSuccess (响应结果) {String} bankAccountList.bankAcctPre
     * @apiSuccess (响应结果) {Number} bankAccountList.bankAcctHash
     * @apiSuccess (响应结果) {String} bankAccountList.bankCode
     * @apiSuccess (响应结果) {String} bankAccountList.bankRegionCode
     * @apiSuccess (响应结果) {String} bankAccountList.bankRegionName
     * @apiSuccess (响应结果) {String} bankAccountList.bankBranchName
     * @apiSuccess (响应结果) {String} bankAccountList.bankAcctStatus
     * @apiSuccess (响应结果) {String} bankAccountList.bankAcctVrfyStat
     * @apiSuccess (响应结果) {String} bankAccountList.acctIdentifyStat
     * @apiSuccess (响应结果) {String} bankAccountList.provCode
     * @apiSuccess (响应结果) {String} bankAccountList.cityCode
     * @apiSuccess (响应结果) {String} bankAccountList.bankAcctName
     * @apiSuccess (响应结果) {String} bankAccountList.fewPmtStat
     * @apiSuccess (响应结果) {String} bankAccountList.paySign
     * @apiSuccess (响应结果) {String} bankAccountList.paySignDt
     * @apiSuccess (响应结果) {String} bankAccountList.limitPerTime
     * @apiSuccess (响应结果) {String} bankAccountList.limitPerDay
     * @apiSuccess (响应结果) {Number} bankAccountList.singleLimitAmount
     * @apiSuccess (响应结果) {Number} bankAccountList.singleMinLimitAmount
     * @apiSuccess (响应结果) {Number} bankAccountList.residueQuota
     * @apiSuccess (响应结果) {Number} bankAccountList.currentMaxAmt
     * @apiSuccess (响应结果) {Number} bankAccountList.dailyTotleLimitSum
     * @apiSuccess (响应结果) {String} bankAccountList.bankName
     * @apiSuccess (响应结果) {String} bankAccountList.provName
     * @apiSuccess (响应结果) {String} bankAccountList.spNumber
     * @apiSuccess (响应结果) {String} bankAccountList.defaultFlag
     * @apiSuccess (响应结果) {String} bankAccountList.cityName
     * @apiSuccess (响应结果) {Number} bankAccountList.discount
     * @apiSuccess (响应结果) {String} bankAccountList.certifiedPayFlag
     * @apiSuccess (响应结果) {String} bankAccountList.quotaIncreaseFlag
     * @apiSuccess (响应结果) {Number} bankAccountList.raisedQuota
     * @apiSuccess (响应结果) {Number} bankAccountList.availAmt
     * @apiSuccess (响应结果) {Number} bankAccountList.totalIncome
     * @apiSuccess (响应结果) {Number} bankAccountList.unCarryOverIncome
     * @apiSuccess (响应结果) {Number} bankAccountList.depositUnackAmt
     * @apiSuccess (响应结果) {Number} bankAccountList.totalAmt
     * @apiSuccess (响应结果) {String} bankAccountList.mobileBank
     * @apiSuccess (响应结果) {String} bankAccountList.mobileBankDigest
     * @apiSuccess (响应结果) {String} bankAccountList.mobileBankMask
     * @apiSuccess (响应结果) {String} bankAccountList.tradeFlag
     * @apiSuccess (响应结果) {Array} bankAccountList.prodQuotaList
     * @apiSuccess (响应结果) {String} bankAccountList.prodQuotaList.prodLqdType
     * @apiSuccess (响应结果) {String} bankAccountList.prodQuotaList.prodType
     * @apiSuccess (响应结果) {String} bankAccountList.prodQuotaList.residueQuota
     * @apiSuccess (响应结果) {String} bankAccountList.prodQuotaList.currentMaxAmt
     * @apiSuccess (响应结果) {String} bankAccountList.prodQuotaList.minAmt
     * @apiSuccess (响应结果) {String} bankAccountList.prodQuotaList.maxAmt
     * @apiSuccess (响应结果) {String} bankAccountList.prodQuotaList.dailyTotleLimit
     * @apiSuccess (响应结果) {String} bankAccountList.prodQuotaList.dailyTotleLimitSum
     * @apiSuccess (响应结果) {String} bankAccountList.changeCardStatus
     * @apiSuccess (响应结果) {String} bankAccountList.recordId
     * @apiSuccess (响应结果) {String} bankAccountList.bankState
     * @apiSuccess (响应结果) {String} bankAccountList.bankLogoUrl
     * @apiSuccess (响应结果) {String} bankAccountList.bankAcctAttr
     * @apiSuccess (响应结果) {String} bankAccountList.unBindShowFlag
     * @apiSuccess (响应结果) {Number} agendDisc 代销折扣率
     * @apiSuccess (响应结果) {Array} highProductActiDiscountModelList 活动折扣率
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.fundCode
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.fundType
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.disCode
     * @apiSuccess (响应结果) {Number} highProductActiDiscountModelList.discountRatio
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.discountType
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.discountRate
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.startDt
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.endDt
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.startTm
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.endTm
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.paySource
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.bankCode
     * @apiSuccess (响应结果) {String} highProductActiDiscountModelList.actiNo
     * @apiSuccess (响应结果) {Array} supportQuickAuthBankCode 支持快捷鉴权的银行代码集合
     * @apiSuccess (响应结果) {Object} subsciptionVO 预约信息
     * @apiSuccess (响应结果) {String} subsciptionVO.id 预约单标识
     * @apiSuccess (响应结果) {Number} subsciptionVO.bookAmount 预约金额
     * @apiSuccess (响应结果) {Number} subsciptionVO.sellVol 赎回金额
     * @apiSuccess (响应结果) {Number} subsciptionVO.bookRatio 折扣率
     * @apiSuccess (响应结果) {Number} subsciptionVO.baseFeeRate 基本费率
     * @apiSuccess (响应结果) {String} subsciptionVO.jjjc 基金简称
     * @apiSuccess (响应结果) {String} subsciptionVO.bankCode 默认银行卡
     * @apiSuccess (响应结果) {String} subsciptionVO.bankProv 默认开户省份
     * @apiSuccess (响应结果) {String} subsciptionVO.bankCity 默认开户城市
     * @apiSuccess (响应结果) {String} subsciptionVO.bankNo 默认银行卡号
     * @apiSuccess (响应结果) {String} subsciptionVO.preIdType 预约id类型
     * @apiSuccess (响应结果) {String} subsciptionVO.discountUseType 折扣类型：1-使用折扣率，2-使用折扣金额      {@link com.howbuy.crm.base.discount.DisCountUseTypeEnum}
     * @apiSuccess (响应结果) {Number} subsciptionVO.discountAmt 折扣金额
     * @apiSuccess (响应结果) {String} feeCalMode 手续费计算类型 0-外扣法；1-内扣法
     * @apiSuccess (响应结果) {Number} activityRate 活动折扣
     * @apiSuccess (响应结果) {String} suportPiggyAppointPay 支持储蓄罐预约支付 0-否 1-是
     * @apiSuccess (响应结果) {String} pmtDt 支付日期
     * @apiSuccess (响应结果) {String} openStartDt 开放开始日期
     * @apiSuccess (响应结果) {String} openEndDt 开放结束日期
     * @apiSuccess (响应结果) {String} isExistsMultiFundTxAcctNo 当前客户是否存在多交易账号,1:是,0:否
     * @apiSuccessExample 响应结果示例
     * {"fundFeeRateList":[{"minFeeVol":5192.206712513291,"maxFeeVol":6194.871993624814,"maxFeeDays":3531,"shareClass":"Ak1","captType":"dc0p","busiCode":"0C42LDP","minFeeAmt":5375.028292280227,"endDt":"V","feeRateFlag":"1V4","maxFee":3029.9445407427593,"tshareClass":"4yrPe7QO","feeRate":6039.055527758848,"minFee":9623.63257791749,"tfundCode":"c8I","getFeeRateMethod":"spfrkoiM","returnCode":"kM","maxFeeAmt":7980.************,"fundCode":"o","invstType":"A7","constantFee":7434.************,"startDt":"YdA3E3vqN","minFeeDays":9895}],"suportPiggyAppointPay":"FxAIHuvWGo","productChannel":"53HR0KTFX","withdrawLimitDto":{"redeFlag":"FkTP4Vw","balanceFastRedeAmt":4402.************,"maxSeparationOrderCount":"JmzPHVJ9Mc","subFlag":"rzSEcaoS","newProductState":"bpWO","savingBoxFundStat":"ibTAphew","quickRedeFlag":"bNfly3G2E"},"shareClass":"moPXIajtH","highProductActiDiscountModelList":[{"discountRate":"ZujO5mT6z","bankCode":"5TLZEFamk","endDt":"l8tV8Sbq4U","paySource":"h","disCode":"31t7","fundType":"9qlWQiStbp","startTm":"SQPau","fundCode":"uSgEYHE","endTm":"coaccQ59XT","actiNo":"30","startDt":"6mR9","discountType":"SsJYUaLENh","discountRatio":9234.************}],"openStartDt":"sIJRdwuEt","lastTimePayChannelCode":"f","lastTimeAccoundId":"bTrHzUHJ","isSupAgentDrawing":"ixhZy8nDty","volDtlDtoList":[{"upgradeLimit":4308.************,"fundInc":984.*************,"bankAcct":"Fy33TOO","bankName":"3W","unCarryOverIncome":3861.***********,"downFundName":"sU","fundAttr":"Zn40","reserveFrzVol":4658.************,"canRedeemFlag":"F7Z","fundCode":"oE","paySign":"w6A","limitPerTime":"5b1I0r1bCI","spNumber":"YWI","depositUnackAmt":3980.************,"bankLogoUrl":"8qUIXZVB","bankCode":"oCIQE","sevenYearRate":3840.************,"availAmt":2812.*************,"tradeFlag":"v","limitPerDay":"7bt","upgradeFlag":"VQsa8MD4q9","downFundCode":"zoTY6QJT","totalIncome":3457.*************,"totalAmt":977.*************,"fundNavDt":"MfbPK","custBankId":"Ivr5B","availRegAmt":8632.************}],"isSupSelfDrawing":"Qqp","showSavingBox":true,"bankAccountList":[{"provName":"yq","bankAcctMask":"sm","prodQuotaList":[{"dailyTotleLimitSum":"m2BuU","currentMaxAmt":"lrnMocqGf","residueQuota":"HInZQILFZd","prodLqdType":"tO","maxAmt":"nfuMAGVmQ","prodType":"Y6Vo","minAmt":"Hh","dailyTotleLimit":"jCpmGu"}],"mobileBankDigest":"9oIJa","discount":6508.************,"unCarryOverIncome":596.*************,"recordId":"C5vwOERH","paySign":"J","limitPerTime":"mA1fQgtJp","bankAcctVrfyStat":"v","spNumber":"fpm2fa6Ww","mobileBankMask":"Pq","defaultFlag":"hdM","availAmt":9561.***********,"tradeFlag":"oMeG5G9","unBindShowFlag":"t8dtTy5H6","totalIncome":1399.*************,"totalAmt":1889.*************,"residueQuota":4249.************,"fewPmtStat":"w7UbJrvC","certifiedPayFlag":"fTRiY0kHl","bankRegionCode":"zQ2qKnocXA","acctIdentifyStat":"P8","cityCode":"zA6p81g","bankAcctPre":"c","bankBranchName":"T7XlImLpS","bankAcct":"f5XnLqv","bankName":"r5qcJ","dailyTotleLimitSum":8606.************,"cityName":"BnbojmGMeX","changeCardStatus":"faw","bankRegionName":"AZ","paySignDt":"wcnw","singleMinLimitAmount":1596.*************,"quotaIncreaseFlag":"H","depositUnackAmt":4859.***********,"bankLogoUrl":"vIZnJ7ZOP","bankCode":"4Z","provCode":"1fDCShx0n","bankAcctName":"qqYl3Q","bankAcctFull":"wArYVpqBW2","bankState":"beY9ST5EI","bankAcctHash":3949,"raisedQuota":8784.***********,"bankAcctDigest":"iDG0X","bankAcctStatus":"4ETY","limitPerDay":"8","currentMaxAmt":9856.************,"mobileBank":"y","singleLimitAmount":2329.*************,"bankAcctAttr":"fMmdxP","custBankId":"XXDYBXGklA"}],"subsciptionVO":{"bankCode":"uhpPCfGKUf","discountAmt":2205.************,"bookAmount":4426.************,"jjjc":"O","sellVol":7031.************,"bankProv":"Ri7qQ","discountUseType":"wfjB5OuVN","baseFeeRate":327.*************,"bankCity":"W","bookRatio":5226.*************,"bankNo":"QYLTF","id":"Txnr5","preIdType":"MgvzNJ"},"supportQuickAuthBankCode":["fn1x"],"agendDisc":5371.************,"pmtDt":"KY","activityRate":7778.************,"openEndDt":"6Arh2sAw","isExistsMultiFundTxAcctNo":"Gidp4","feeCalMode":"6Re4"}
     */
    @RequestMapping("/simu/trade/buyConfirm.htm")
    public void buyConfirm(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 获取请求参数对象
        BuyCmd buyCmd = getCommand(BuyCmd.class);
        ValidateUtil.assertValid(buyCmd);
        String fundCode = buyCmd.getFundCode();

        HighProductInfoModel highProductBaseModel = buyService.getHighProductInfo(fundCode);

        TradeSession loginInfo = getCustSession();
        String txAcctNo = loginInfo.getUser().getCustNo();

        // 查询储蓄罐的取现规则
        PiggyFundTxOpenCfgModel piggyCfg = getPiggyOpenCfg(highProductBaseModel.getPaymentTypeList());

        String investType = StringUtils.isNotBlank(loginInfo.getUser().getInvstType()) ? loginInfo.getUser().getInvstType() : "1";

        // 查询购买预约开放日历
        HighProductAppointmentInfoModel buyAppointmentInfoModel = highProductService.getAppointmentInfoByAppointDate(fundCode, "0",
                highProductBaseModel.getShareClass(), Constants.DIS_CODE_HOWBUY, new Date());

        // 业务代码
        String busiCode = buyService.getBusiCode(highProductBaseModel);
        log.info("buy|fundCode:{}, busiCode:{}", fundCode, busiCode);
        // 费率
        List<HighProductFeeRateModel> fundFeeRateList = highProductService.getHighProductFeeRate(fundCode, investType, busiCode,
                highProductBaseModel.getShareClass());
        //活动费率
        HighProductActiDiscountListModel model = buyService.getActiDiscount(fundCode, highProductBaseModel.getShareClass(), busiCode);
        // 最近一次购买记录
        DealOrderBean dealOrderBean = getLastBuyDeal(txAcctNo, fundCode);
        // 查询储蓄罐卡信息
        List<SavingBoxVolDetail> piggyBankCardList = getPiggyBankCardList(highProductBaseModel, txAcctNo, dealOrderBean);
        // 储蓄罐卡号明文
        getPiggyBankCardPlaintextList(txAcctNo, piggyBankCardList);
        // 查询银行卡列表
        List<CustBankModel> bankCardInfos = getBankCardInfos(highProductBaseModel, txAcctNo, dealOrderBean);
        // 查询支持快捷鉴权的银行卡列表
        Set<String> supportQuickAuthBankCode = getSupportQuickAuthBankCode(bankCardInfos);
        // 银行卡列表重排序：已签约-未签约-不支持快捷
        List<CustBankModel> custBindBankList = sortCustBankInfo(bankCardInfos, supportQuickAuthBankCode);
        // 获取缓存中预约信息
        String buyPreKey = CacheKeyUtil.getBuyPreinfoCacheKey(request, loginInfo.getUser().getHboneNo());
        log.info("buySubmit|buyPreKey:{}", buyPreKey);
        PrebookDetailDto prebookDetailDto = getSetCacheBuyPre(buyPreKey, request, loginInfo, fundCode);
        // 构造返回信息
        BuyConfirmDto dto = buildBuyComfirmDto(piggyCfg, highProductBaseModel, dealOrderBean, piggyBankCardList, fundFeeRateList, custBindBankList, model, prebookDetailDto);
        // 是否存在多交易账号
        QueryIsExistsMultiFundTxAcctNoRequest queryIsExistsMultiFundTxAcctNoRequest = new QueryIsExistsMultiFundTxAcctNoRequest();
        queryIsExistsMultiFundTxAcctNoRequest.setTxAcctNo(txAcctNo);
        queryIsExistsMultiFundTxAcctNoRequest.setFundCode(fundCode);
        QueryIsExistsMultiFundTxAcctNoResponse existMultiFundTxAcctNoResp = queryIsExistsMultiFundTxAcctNoFacade.query(queryIsExistsMultiFundTxAcctNoRequest);
        dto.setIsExistsMultiFundTxAcctNo(existMultiFundTxAcctNoResp.getExistsMultiFundTxAcctNo() != null && existMultiFundTxAcctNoResp.getExistsMultiFundTxAcctNo() ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        // 储蓄罐预约支付日期
        if (buyAppointmentInfoModel != null && StringUtils.isNotEmpty(buyAppointmentInfoModel.getPayDeadlineDtm())) {
            dto.setPmtDt(buyAppointmentInfoModel.getPayDeadlineDtm().substring(0, 8));
        }

        // 设置开放开始和结束日期
        if (buyAppointmentInfoModel != null) {
            dto.setOpenStartDt(buyAppointmentInfoModel.getOpenStartDt());
            dto.setOpenEndDt(buyAppointmentInfoModel.getOpenEndDt());
        } else {
            dto.setOpenEndDt("");
        }

        //加密核心信息
        if (CgiParamUtil.isH5() && CgiParamUtil.isEncParam()) {
            dto.setLastTimeAccoundId(getEncParamters(dto.getLastTimeAccoundId()));
            if (dto.getVolDtlDtoList() != null && dto.getVolDtlDtoList().size() > 0) {
                for (SavingBoxVolDetail boxVolDetail : dto.getVolDtlDtoList()) {
                    boxVolDetail.setBankAcct((boxVolDetail.getBankAcct()));
                }
            }

            if (dto.getBankAccountList() != null && dto.getBankAccountList().size() > 0) {
                for (CustBankModel custBankModel : dto.getBankAccountList()) {
                    custBankModel.setBankAcct(getEncParamters(custBankModel.getBankAcct()));
                    custBankModel.setBankAcctFull(getEncParamters(custBankModel.getBankAcctFull()));
                    custBankModel.setMobileBank(getEncParamters(custBankModel.getMobileBank()));
                }
            }
        }

        write(dto, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    private void getPiggyBankCardPlaintextList(String txAcctNo, List<SavingBoxVolDetail> piggyBankCardList) {
        if (piggyBankCardList != null && !piggyBankCardList.isEmpty()) {
            List<CustBankModel> custBankModelList = bankCardService.queryBindBankCards(txAcctNo);
            Map<String, CustBankModel> bankCardMap = new HashMap<>();
            for (CustBankModel custBankModel : custBankModelList) {
                bankCardMap.put(custBankModel.getCustBankId(), custBankModel);
            }
            for (SavingBoxVolDetail savingBoxVolDetail : piggyBankCardList) {
                CustBankModel custBankModel = bankCardMap.get(savingBoxVolDetail.getCustBankId());
                if (custBankModel != null) {
                    savingBoxVolDetail.setBankAcct(custBankModel.getBankAcctMask());
                }
            }
        }
    }

    /**
     * @api {post}  /simu/trade/buySubmit.htm 下单（已作废，用buySubmitMerge.htm）
     * @apiGroup buy
     * @APIName /simu/trade/buySubmit.htm
     * @apiDescription 下单（已作废，用buySubmitMerge.htm）
     * @apiParam {String} fundCode 基金代码
     * @apiParam {String} paymentType 支付方式 06：储蓄罐  04：银行卡代扣 01-自划款
     * @apiParam {BigDecimal} payAmount 支付金额
     * @apiParam {BigDecimal} tradeFee 手续费
     * @apiParam {String} bankAccountId  银行账号
     * @apiParam {String} txPassword 交易密码
     * @apiParam {String} bankBranchName 支行名称
     * @apiParam {String} isBoughtFlag 1-首次购买 2-追加购买
     * @apiParam {String} authenticateSeriousNo  快捷鉴权流水号
     * @apiParam {String} authenticateCode     快捷鉴权验证码
     * @apiParam {String} oldMobile             老的手机号
     * @apiParam {String} mobile                输入的手机号
     * @apiParam {String} tradeAmount    交易金额
     * @apiSuccess {String} [code]  返回编码
     * @apiSuccess {String} [desc]  返回消息
     * @apiSuccess {Object}  [subsOrPurWebResponse] 返回结果
     * @apiSuccess (subsOrPurWebResponse) {String} dealNo 订单号
     * @apiSuccess (subsOrPurWebResponse) {String} confirmDt 预计确认日期
     * @apiSuccess (subsOrPurWebResponse) {String} dealDtm 下单日期YYYYMMDDHHMMSS
     * @apiSuccess (subsOrPurWebResponse) {String} taTradeDt 上报TA日期YYYYMMDD
     * @apiSuccessExample {json} Response Example { "body": {
     * "mobile":"**********" }, "desc": "成功", "code": "0000"
     * }
     */
    @RequestMapping("/simu/trade/buySubmit.htm")
    public void buySubmit(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 返回dto
        BuySubmitDto dto = new BuySubmitDto();
        TradeSession loginInfo = getCustSession();
        // 请求cmd
        BuySubmitCmd cmd = getCommand(BuySubmitCmd.class);
        ValidateUtil.assertValid(cmd);
        cmd.setBankBranchName(java.net.URLDecoder.decode(cmd.getBankBranchName(), "utf-8"));
        BigDecimal payAmt = cmd.getPayAmount();// 支付金额
        BigDecimal tradeAmt = cmd.getTradeAmount();
        BigDecimal fee = payAmt.subtract(tradeAmt);// 手续费
        String fundCode = cmd.getFundCode();
        String txAcctNo = loginInfo.getUser().getCustNo();
        String hboneNo = loginInfo.getUser().getHboneNo();
        // 产品基本信息
        HighProductInfoModel model = buyService.getHighProductInfo(fundCode);
        // 支付银行卡信息
        PayDetailDto payDto = getPayDetailDto(txAcctNo, cmd.getCustBankId(), cmd.getPaymentType(), model.getProductChannel());
        LOG.info("payDto", JSON.toJSON(payDto));
        if (StringUtils.isBlank(cmd.getMobile())) {
            cmd.setMobile(null);
        }
        if (StringUtils.isBlank(cmd.getOldMobile())) {
            cmd.setOldMobile(null);
        }

        // 验证码校验
        validateAuthCode(cmd, payDto, loginInfo, cmd.getAuthenticateCode(), cmd.getAuthenticateSeriousNo());
        // 支付金额校验
        validPayAmtLimit(cmd.getPaymentType(), payDto, payAmt);
        // 获取风险匹配标识
        String matchRiskLevelFlag = buyService.getMatchRiskFlag(hboneNo, model, DisCodeEnum.HM.getCode());
        // 交易密码
        String txPassword = getString("txPassword");
        if (StringUtils.isNotBlank(txPassword)) {
            SubsOrPurWebRequest subsOrPurWebRequest = new SubsOrPurWebRequest();
            subsOrPurWebRequest.setTxPwd(EncryptUtil.getInstance(EncryptAlgEnum.Des).encMsg(txPassword.getBytes("utf-8")));
            String reqIp = WebUtil.getCustIP(request);
            subsOrPurWebRequest.setOperIp(reqIp);
            subsOrPurWebRequest.setDataTrack(UUID.randomUUID().toString());
            subsOrPurWebRequest.setTxAcctNo(txAcctNo);
            subsOrPurWebRequest.setExternalDealNo(UUID.randomUUID().toString());
            subsOrPurWebRequest.setPaymentType(cmd.getPaymentType()); // 支付方式
            subsOrPurWebRequest.setCpAcctNo(payDto.getCustBankId());// 资金账号
            subsOrPurWebRequest.setAppAmt(payAmt.setScale(2, BigDecimal.ROUND_HALF_UP));
            subsOrPurWebRequest.setEsitmateFee(fee.setScale(2, BigDecimal.ROUND_DOWN));
            subsOrPurWebRequest.setRiskFlag(YesOrNoEnum.NO.getCode().equals(matchRiskLevelFlag) ? "1" : "0");// 二次确认
            subsOrPurWebRequest.setFundCode(fundCode);
            subsOrPurWebRequest.setFundShareClass(model.getShareClass());
            subsOrPurWebRequest.setProtocolType(ProtocolTypeEnum.HIGH_FUND.getCode());
            subsOrPurWebRequest.setAppDt(DateUtils.formatToString(new Date(), "yyyyMMdd"));
            subsOrPurWebRequest.setAppTm(DateUtils.formatToString(new Date(), "HHmmss"));
            subsOrPurWebRequest.setDisCode(DisCodeEnum.HM.getCode());
            // 获取缓存中使用预约信
            String buyPreKey = CacheKeyUtil.getBuyPreinfoCacheKey(request, hboneNo);
            log.info("buySubmit|buyPreKey:{}", buyPreKey);
            PrebookDetailDto prebookDetailDto = getCacheBuyPre(buyPreKey);
            subsOrPurWebRequest.setAppointmentDealNo(prebookDetailDto.getId());

            SubsOrPurWebResponse subsOrPurWebResponse = subsOrPurWebFacade.execute(subsOrPurWebRequest);

            if (null != subsOrPurWebResponse && "Z0000000".equals(subsOrPurWebResponse.getReturnCode())) {// 成功
                // 删除购买预约缓存key
                cacheService.remove(buyPreKey);
                dto.setCode("0000");
                dto.setDesc("成功");
                dto.setSubsOrPurWebResponse(subsOrPurWebResponse);
            } else {
                // 删除购买预约缓存key
                cacheService.remove(buyPreKey);
                if (subsOrPurWebResponse != null) {
                    if (ERROR_CODE.equals(subsOrPurWebResponse.getReturnCode())) {// 交易密码错误
                        dto.setCode(ERROR_CODE);
                        dto.setDesc("密码出错");
                    } else {
                        dto.setCode(subsOrPurWebResponse.getReturnCode());
                        dto.setDesc(subsOrPurWebResponse.getDescription());
                    }
                }

            }
        }
        write(dto, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * @api {post}  /simu/trade/checkAuthCode.htm 验证码校验
     * @apiGroup buy
     * @APIName /simu/trade/checkAuthCode.htm
     * @apiDescription 验证码校验
     * @apiParam {String} fundCode 基金代码
     * @apiParam {BigDecimal} payAmount 总支付金额（交易金额+预估手续费）
     * @apiParam {BigDecimal} tradeAmount 总交易金额
     * @apiParam {String} txPassword 交易密码
     * @apiParam {String} isBoughtFlag 1-首次购买 2-追加购买
     * @apiParam {String} authenticateSeriousNo  快捷鉴权流水号
     * @apiParam {String} authenticateCode     快捷鉴权验证码
     * @apiParam {String} [oldMobile]             老的手机号
     * @apiParam {String} mobile                输入的手机号
     * @apiParam {String} sealId                签章id
     * @apiParam {String} formNo                表单单号
     * @apiParam {String} disCode                分销渠道
     * @apiParam {list} payList 支付列表
     * @apiParam (payList) {String} paymentType 支付方式 06：储蓄罐  04：银行卡代扣 01-自划款
     * @apiParam (payList) {String} payAmount 支付金额
     * @apiParam (payList) {String} custBankId  银行账号
     * @apiParam (payList) {String} authFlag 鉴权标识，必填且仅有一个是 0-否；1-是
     * @apiSuccess {String} [code]  返回编码
     * @apiSuccess {String} [desc]  返回消息
     * @apiSuccessExample {json} Response Example { "body": "success", "desc": "成功", "code": "0000"
     * }
     */
    @RequestMapping("/simu/trade/checkAuthCode.htm")
    public void checkAuthCode(HttpServletRequest request, HttpServletResponse response) throws Exception {
        UserInfo user = getCustSession().getUser();
        // 请求cmd
        BuySubmitMergeCmd cmd = getCommand(BuySubmitMergeCmd.class);
        validateBuySubmitMergeParams(cmd);
        // 产品基本信息
        HighProductInfoModel model = buyService.getHighProductInfo(cmd.getFundCode());
        // 支付银行卡信息
        PayDetailDto payDto = getPayDetailDto(user.getCustNo(), cmd, model.getProductChannel());
        LOG.info("支付银行卡信息:{}", JSON.toJSON(payDto));
        if (StringUtils.isBlank(cmd.getMobile())) {
            cmd.setMobile(null);
        }
        if (StringUtils.isBlank(cmd.getOldMobile())) {
            cmd.setOldMobile(null);
        }
        if (StringUtils.isBlank(cmd.getDisCode())) {
            cmd.setDisCode(DisCodeEnum.HM.getCode());
        }
        // 验证码校验
        validateAuthCode(cmd, payDto, user, cmd.getAuthenticateCode(), cmd.getAuthenticateSeriousNo());
        write("success", CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }


    /**
     * @api {post}  /simu/trade/buySubmitMerge.htm 下单合并支付
     * @apiGroup buy
     * @APIName /simu/trade/buySubmitMerge.htm
     * @apiDescription 下单合并支付
     * @apiParam {String} fundCode 基金代码
     * @apiParam {BigDecimal} payAmount 总支付金额（交易金额+预估手续费）
     * @apiParam {BigDecimal} tradeAmount 总交易金额
     * @apiParam {String} txPassword 交易密码
     * @apiParam {String} isBoughtFlag 1-首次购买 2-追加购买
     * @apiParam {String} authenticateSeriousNo  快捷鉴权流水号
     * @apiParam {String} authenticateCode     快捷鉴权验证码
     * @apiParam {String} [oldMobile]             老的手机号
     * @apiParam {String} mobile                输入的手机号
     * @apiParam {String} sealId                签章id
     * @apiParam {String} formNo                表单单号
     * @apiParam {String} serviceEntityName     服务主体名
     * @apiParam {String} fpqcCode              基金从业资格编号
     * @apiParam {String} disCode                分销渠道
     * @apiParam {list} payList 支付列表
     * @apiParam (payList) {String} paymentType 支付方式 06：储蓄罐  04：银行卡代扣 01-自划款
     * @apiParam (payList) {String} payAmount 支付金额
     * @apiParam (payList) {String} custBankId  银行账号
     * @apiParam (payList) {String} authFlag 鉴权标识，必填且仅有一个是 0-否；1-是
     * @apiParam {String} riskHintConfirmDtm 风测提醒确定时间 yyyyMMddHHmmss
     * @apiParam {String} investorQualifiedHintConfirmDtm 投资者类型认证提醒确认时间 yyyyMMddHHmmss
     * @apiSuccess {String} [code]  返回编码
     * @apiSuccess {String} [desc]  返回消息
     * @apiSuccess {Object}  [subsOrPurWebResponse] 返回结果
     * @apiSuccess (subsOrPurWebResponse) {String} dealNo 订单号
     * @apiSuccess (subsOrPurWebResponse) {String} confirmDt 预计确认日期
     * @apiSuccess (subsOrPurWebResponse) {String} dealDtm 下单日期YYYYMMDDHHMMSS
     * @apiSuccess (subsOrPurWebResponse) {String} taTradeDt 上报TA日期YYYYMMDD
     * @apiSuccessExample {json} Response Example { "body": {
     * "mobile":"**********" }, "desc": "成功", "code": "0000"
     * }
     */
    @RequestMapping("/simu/trade/buySubmitMerge.htm")
    public void buySubmitMerge(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 返回dto
        BuySubmitDto dto = new BuySubmitDto();
        UserInfo user = getCustSession().getUser();
        // 请求cmd
        BuySubmitMergeCmd cmd = getCommand(BuySubmitMergeCmd.class);
        validateBuySubmitMergeParams(cmd);
        // 产品基本信息
        HighProductInfoModel model = buyService.getHighProductInfo(cmd.getFundCode());
        // 支付银行卡信息
        PayDetailDto payDto = getPayDetailDto(user.getCustNo(), cmd, model.getProductChannel());
        LOG.info("支付银行卡信息:{}", JSON.toJSON(payDto));
        if (StringUtils.isBlank(cmd.getMobile())) {
            cmd.setMobile(null);
        }
        if (StringUtils.isBlank(cmd.getOldMobile())) {
            cmd.setOldMobile(null);
        }
        if (StringUtils.isBlank(cmd.getDisCode())) {
            cmd.setDisCode(RemoteParametersProvider.getDistributionCode());
        }
        // 验证码校验
        validateAuthCode(cmd, payDto, user, cmd.getAuthenticateCode(), cmd.getAuthenticateSeriousNo());
        // 支付金额校验
        validPayAmtLimit(cmd, payDto);
        // 校验kyc信息
        buyService.checkKyc(user, WebUtil.getCustIP(request), cmd.getDisCode(), cmd.getFundCode());
        // 交易密码
        String txPassword = getString("txPassword");
        if (StringUtils.isNotBlank(txPassword)) {
            // 获取缓存中使用预约信息
            String buyPreKey = CacheKeyUtil.getBuyPreinfoCacheKey(request, user.getHboneNo());
            log.info("buySubmit|buyPreKey:{}", buyPreKey);
            PrebookDetailDto prebookDetailDto = getCacheBuyPre(buyPreKey);
            SubsOrPurMergeWebRequest submitRequest = getSubmitRequest(request, cmd, user, prebookDetailDto, model);
            SubsOrPurMergeWebResponse subsOrPurWebResponse = subsOrPurMergeWebFacade.execute(submitRequest);
            // 成功
            if ("Z0000000".equals(subsOrPurWebResponse.getReturnCode())) {
                // 删除购买预约缓存key
                cacheService.remove(buyPreKey);
                dto.setCode("0000");
                dto.setDesc("成功");
                dto.setSubsOrPurWebResponse(subsOrPurWebResponse);
                dto.setmBusiCode(subsOrPurWebResponse.getmBusiCode());
            } else {
                // 删除购买预约缓存key
                cacheService.remove(buyPreKey);
                // 交易密码错误
                if (ERROR_CODE.equals(subsOrPurWebResponse.getReturnCode())) {
                    dto.setCode(ERROR_CODE);
                    dto.setDesc("密码出错");
                } else {
                    dto.setCode(subsOrPurWebResponse.getReturnCode());
                    dto.setDesc(subsOrPurWebResponse.getDescription());
                }
            }
        }
        write(dto, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 获取上报Request对象
     *
     * @param request
     * @param cmd
     * @param user
     * @param prebookDetailDto
     * @param productInfo
     * @return com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurmergeweb.SubsOrPurMergeWebRequest
     * @author: huaqiang.liu
     * @date: 2021/5/25 16:56
     * @since JDK 1.8
     */
    private SubsOrPurMergeWebRequest getSubmitRequest(HttpServletRequest request, BuySubmitMergeCmd cmd, UserInfo user,
                                                      PrebookDetailDto prebookDetailDto, HighProductInfoModel productInfo) {
        // 支付金额
        BigDecimal payAmt = cmd.getPayAmount();
        BigDecimal tradeAmt = cmd.getTradeAmount();
        // 手续费
        BigDecimal fee = payAmt.subtract(tradeAmt);
        // 获取风险匹配标识
        String matchRiskLevelFlag = buyService.getMatchRiskFlag(user.getHboneNo(), productInfo, DisCodeEnum.HM.getCode());

        SubsOrPurMergeWebRequest submitRequest = new SubsOrPurMergeWebRequest();
        String txPassword = getString("txPassword");
        submitRequest.setTxPwd(EncryptUtil.getInstance(EncryptAlgEnum.Des).encMsg(txPassword.getBytes(StandardCharsets.UTF_8)));
        String reqIp = WebUtil.getCustIP(request);
        submitRequest.setOperIp(reqIp);
        submitRequest.setDataTrack(UUID.randomUUID().toString());
        submitRequest.setTxAcctNo(user.getCustNo());
        if (StringUtils.isNotEmpty(cmd.getExternalDealNo())) {
            submitRequest.setExternalDealNo(cmd.getExternalDealNo());
        } else {
            submitRequest.setExternalDealNo(UUID.randomUUID().toString());
        }
        submitRequest.setServiceEntityName(cmd.getServiceEntityName());
        submitRequest.setFpqcCode(cmd.getFpqcCode());
        submitRequest.setDisCode(RemoteParametersProvider.getDistributionCode());
        submitRequest.setAppAmt(payAmt.setScale(2, BigDecimal.ROUND_HALF_UP));
        submitRequest.setEsitmateFee(fee.setScale(2, BigDecimal.ROUND_DOWN));
        // 二次确认
        submitRequest.setRiskFlag(YesOrNoEnum.NO.getCode().equals(matchRiskLevelFlag) ? "1" : "0");
        submitRequest.setFundCode(cmd.getFundCode());
        submitRequest.setFundShareClass(productInfo.getShareClass());
        submitRequest.setProtocolType(ProtocolTypeEnum.HIGH_FUND.getCode());
        submitRequest.setAppDt(DateUtils.formatToString(new Date(), "yyyyMMdd"));
        submitRequest.setAppTm(DateUtils.formatToString(new Date(), "HHmmss"));
        submitRequest.setAppointmentDealNo(prebookDetailDto.getId());
        submitRequest.setSealId(cmd.getSealId());

        // 风险提示确认时间
        submitRequest.setRiskHintConfirmDtm(cmd.getRiskHintConfirmDtm());
        submitRequest.setInvestorQualifiedConfirmDtm(cmd.getInvestorQualifiedHintConfirmDtm());

        submitRequest.setSubsAmt(cmd.getSubsAmt());
        submitRequest.setFormNo(cmd.getFormNo());
        List<PayInfoBean> payList = new ArrayList<>(cmd.getPayList().size());
        submitRequest.setPayList(payList);
        for (BuySubmitMergeCmd.PayBean pay : cmd.getPayList()) {
            PayInfoBean payInfoBean = new PayInfoBean();
            // 支付方式
            payInfoBean.setPaymentType(pay.getPaymentType());
            // 支付金额
            payInfoBean.setPayAmt(pay.getPayAmount());
            // 资金账号
            payInfoBean.setCpAcctNo(pay.getCustBankId());
            payList.add(payInfoBean);
        }

        return submitRequest;
    }

    /**
     * 校验合并购买参数
     *
     * @param cmd
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/5/25 16:13
     * @since JDK 1.8
     */
    private void validateBuySubmitMergeParams(BuySubmitMergeCmd cmd) {
        ValidateUtil.assertValid(cmd);
        if (cmd.getPayList() == null || cmd.getPayList().isEmpty()) {
            throw new BizException(BizErrorEnum.REQUEST_PARAMS_ERROR.getCode(), "支付列表不能为空");
        }
        int authNum = 0;
        for (BuySubmitMergeCmd.PayBean pay : cmd.getPayList()) {
            if (YesOrNoEnum.YES.getCode().equals(pay.getAuthFlag())) {
                authNum++;
            }
        }
        if (authNum != 1) {
            throw new BizException(BizErrorEnum.REQUEST_PARAMS_ERROR.getCode(), "支付列表必须只有一个鉴权标识");
        }
        // 风险提醒确认时间校验
        if(StringUtils.isEmpty(cmd.getInvestorQualifiedHintConfirmDtm())
                && StringUtils.isEmpty(cmd.getRiskHintConfirmDtm())
                && simuCcmsServiceRegister.isVerifyRiskHintConfirmDtm()) {
            throw new BizException(BizErrorEnum.REQUEST_PARAMS_ERROR.getCode(), "风险提醒确认时间与投资者认证提醒确认时间必填其一");
        }
    }

    /**
     * @api {get}  /simu/trade/getconcifgsetuplist.htm 投资者声明
     * @apiGroup buy
     * @APIName /simu/trade/getconcifgsetuplist.htm
     * @apiDescription 投资者声明
     * @apiParam {String} fundCode 基金代码
     * @apiParam {String} shareClass 份额类型
     * @apiSuccess {String} [fundCode]  基金代码
     * @apiSuccess {String} [shareClass]  份额类型
     * @apiSuccess {String} [configSetup1]  配置步骤1
     * @apiSuccess {String} [configSetup2]  配置步骤2
     * @apiSuccess {String} [configSetup3]  配置步骤3
     * @apiSuccess {String} [configSetup4]  配置步骤4
     * @apiSuccess {String} [tempId]  模板ID
     * @apiSuccess {String} [tempContent1]  模板内容1
     * @apiSuccessExample {json} Response Example { "body": {
     * "mobile":"**********" }, "desc": "成功", "code": "0000"
     * }
     */
    @RequestMapping("/simu/trade/getconcifgsetuplist.htm")
    public void getconcifgsetuplist(HttpServletRequest request, HttpServletResponse response) throws IOException {
        GetConcifgSetUpCmd cmd = getCommand(GetConcifgSetUpCmd.class);
        ValidateUtil.assertValid(cmd);
        FundEcontractRiskInfoModel fundEcontractRiskInfoModel = highProductService.getFundEcontractRiskInfoByMidProductId(cmd.getFundCode(), cmd.getShareClass());
        log.info("getconcifgsetuplist|fundcode:{},fundEcontractRiskInfoModel:{}", cmd.getFundCode(), JSON.toJSONString(fundEcontractRiskInfoModel));
        if (fundEcontractRiskInfoModel != null) {
            StringBuilder strBuilder = new StringBuilder();
            if (!StringUtils.isEmpty(fundEcontractRiskInfoModel.getTempContent1())) {
                strBuilder.append(fundEcontractRiskInfoModel.getTempContent1().replaceAll("#amp;", "&amp;").replaceAll("#lqj;", "<")
                        .replaceAll("#rqj;", ">").replaceAll("#quot;", "\"").replaceAll("#qut;", "/").replaceAll("<[^>]*>", ""));
            }
            if (!StringUtils.isEmpty(fundEcontractRiskInfoModel.getTempContent2())) {
                strBuilder.append(fundEcontractRiskInfoModel.getTempContent2().replaceAll("#amp;", "&amp;").replaceAll("#lqj;", "<")
                        .replaceAll("#rqj;", ">").replaceAll("#quot;", "\"").replaceAll("#qut;", "/").replaceAll("<[^>]*>", ""));
                // 置空，无需返回前端
                fundEcontractRiskInfoModel.setTempContent2("");
            }
            if (!StringUtils.isEmpty(fundEcontractRiskInfoModel.getTempContent3())) {
                strBuilder.append(fundEcontractRiskInfoModel.getTempContent3().replaceAll("#amp;", "&amp;").replaceAll("#lqj;", "<")
                        .replaceAll("#rqj;", ">").replaceAll("#quot;", "\"").replaceAll("#qut;", "/").replaceAll("<[^>]*>", ""));
                // 置空，无需返回前端
                fundEcontractRiskInfoModel.setTempContent3("");
            }
            // 模板内容拼接到tmpContent1返回给前端
            fundEcontractRiskInfoModel.setTempContent1(strBuilder.toString());
        } else {
            fundEcontractRiskInfoModel = new FundEcontractRiskInfoModel();
        }
        write(fundEcontractRiskInfoModel, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    // 过滤银行代码分割符
    private static final String BANK_CODE_SPLIT = "\\|";

    /**
     * @api {POST} /simu/trade/requestMobile.htm 购买获取银行预留手机号
     * @apiVersion 1.0.0
     * @apiGroup buy
     * @apiName requestMobile
     * @apiDescription 购买获取银行预留手机号
     * @apiParam (请求参数) {String} paymentType 01-自划款 04-银行卡 06-储蓄罐
     * @apiParam (请求参数) {String} custBankId
     * @apiParamExample 请求参数示例
     * custBankId=7rPSQ&paymentType=dJN4NKt
     * @apiSuccess (响应结果) {String} mobile 手机号
     * @apiSuccess (响应结果) {Number} date 日期
     * @apiSuccess (响应结果) {String} eAuth e签宝短信鉴权 0-否 1-是
     * @apiSuccessExample 响应结果示例
     * {"date":*************,"eAuth":"JUgISeUxEI","mobile":"PUjtZ"}
     */
    @RequestMapping("/simu/trade/requestMobile.htm")
    public void requestMobile(HttpServletRequest request, HttpServletResponse response) throws IOException {
        TradeSession loginInfo = getCustSession();
        String txAcctNo = loginInfo.getUser().getTxAcctNo();
        String disCode = RemoteParametersProvider.getDistributionCode();
        RequestMobileCmd cmd = getCommand(RequestMobileCmd.class);
        ValidateUtil.assertValid(cmd);
        // 1.查询支付银行卡信息
        QueryBankCardInfoResponse bankInfo = getBankCardInfo(txAcctNo, cmd.getCustBankId(), disCode);
        // 2.查询银行卡预留手机号
        String mobile = getBankMobile(loginInfo.getUser().getHboneNo(), cmd.getCustBankId(), disCode);

        Set<String> howBuyMobiles = getHowBuyMobile(loginInfo, disCode);

        // 好买发送短信，校验手机号是否好买预留
        // 获取银行对应短信鉴权方式
        String bankAuthType = getBankAuthType(bankInfo.getBankCode());

        boolean sendWithHboneMobile = isSendWithHboneNo(bankAuthType);
        if (!IdTypeEnum.IDCARD.getValue().equals(loginInfo.getUser().getIdType())) {
            sendWithHboneMobile = true;
        }
        if (sendWithHboneMobile && CollectionUtils.isEmpty(howBuyMobiles)) {
            throw new BizException(BizErrorEnum.HIGH_USER_MOBILE_NOT_EXIST);
        }

        // e签宝短信鉴权
        String eAuth = YesOrNoEnum.NO.getCode();

        if (CGISimuConstants.SEND_MSG_CHANNEL_ES.equals(bankAuthType)) {
            eAuth = YesOrNoEnum.YES.getCode();
        }

        log.info("sendWithHboneMobile：{}", sendWithHboneMobile);

        // 好买短信，返回一帐通手机号
        if (sendWithHboneMobile || StringUtils.isEmpty(mobile)) {
            CustMobileModel custMobileModel = accPlaintextService.queryCustMobile(null, loginInfo.getUser().getHboneNo(), disCode);
            mobile = custMobileModel.getMobile();
        }

        // 返回对象
        RequestMobileDto dto = new RequestMobileDto();
        dto.setMobile(mobile);
        dto.setDate(new Date());
        dto.seteAuth(eAuth);

        //加密核心信息
        if (CgiParamUtil.isH5() && CgiParamUtil.isEncParam()) {
            dto.setMobile(getEncParamters(dto.getMobile()));
        }

        write(dto, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * @param bankCode
     * @return java.lang.String
     * @Description 查询银行鉴权通道
     * <AUTHOR>
     * @Date 2020/8/14 18:34
     **/
    private String getBankAuthType(String bankCode) {
        Set<BankCodeAndAuthTypeDto> bankCodeFilterSet = buildBankCodeFilterSet(simuCcmsServiceRegister.getBankCodeFilter());
        if (CollectionUtils.isNotEmpty(bankCodeFilterSet)) {
            for (BankCodeAndAuthTypeDto bankCodeAndAuthType : bankCodeFilterSet) {
                if (bankCodeAndAuthType.getBankCode().equals(bankCode)) {
                    return bankCodeAndAuthType.getAuthType();
                }
            }
        }

        return simuCcmsServiceRegister.getAuthChannel();
    }

    private boolean isSendWithHboneNo(String bankAuthType) {
        if (CGISimuConstants.SEND_MSG_CHANNEL_ES.equals(bankAuthType) || CGISimuConstants.SEND_MSG_CHANNEL_HOWBUY.equals(bankAuthType)) {
            // e签宝发送短信或好买
            return true;
        }

        return false;
    }

    /**
     * @param bankCodeFilter
     * @return java.util.Set<java.lang.String>
     * @Description 构建过滤银行卡集合
     * <AUTHOR>
     * @Date 2018/10/29 11:13
     **/
    private Set<BankCodeAndAuthTypeDto> buildBankCodeFilterSet(String bankCodeFilter) {
        log.info("SendAuthMsgFacadeService|buildBankCodeFilterSet|bankCodeFilter:{}", bankCodeFilter);
        Set<BankCodeAndAuthTypeDto> bankCodeFilterSet = new HashSet<BankCodeAndAuthTypeDto>();
        if (com.howbuy.tms.common.utils.StringUtils.isEmpty(bankCodeFilter)) {
            return bankCodeFilterSet;
        }

        String[] bankCodeArr = bankCodeFilter.trim().split(BANK_CODE_SPLIT);
        if (bankCodeArr.length <= 0) {
            return bankCodeFilterSet;
        }

        for (String bankCode : bankCodeArr) {
            String[] bankCodeAndAuthTypeArr = bankCode.trim().split("_");
            if (bankCodeAndAuthTypeArr.length >= 2) {
                BankCodeAndAuthTypeDto bankCodeAndAuthType = new BankCodeAndAuthTypeDto();
                bankCodeAndAuthType.setBankCode(bankCodeAndAuthTypeArr[0]);
                bankCodeAndAuthType.setAuthType(bankCodeAndAuthTypeArr[1]);
                bankCodeFilterSet.add(bankCodeAndAuthType);

            } else {
                log.info("bankCodeAndAuthType length < 2 , bankCode:{}", bankCode);
            }
        }

        return bankCodeFilterSet;
    }


    /**
     * @api {GET} /simu/trade/getPayApplyResult.htm getPayApplyResult
     * @apiVersion 1.0.0
     * @apiGroup BuyController
     * @apiName getPayApplyResult
     * @apiDescription 下单结果页
     * @apiParam (请求参数) {String} hbOneNo 一账通
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} dealNo 订单号
     * @apiParam (请求参数) {String} operCorpid copId
     * @apiParamExample 请求参数示例
     * hbOneNo=A&txAcctNo=4e&dealNo=kAQI&operCorpid=lYNJp
     * @apiSuccess (响应结果) {Object} orderApplyInfoDto 产品购买申请结果信息
     * @apiSuccess (响应结果) {String} orderApplyInfoDto.fundCode 产品编码
     * @apiSuccess (响应结果) {String} orderApplyInfoDto.disCode 分销编码
     * @apiSuccess (响应结果) {String} orderApplyInfoDto.fundName 产品名称
     * @apiSuccess (响应结果) {String} orderApplyInfoDto.peDivideCallFlag 是否分次CALL款股权产品 0-否 1是
     * @apiSuccess (响应结果) {String} orderApplyInfoDto.payEndDt 打款截止日期
     * @apiSuccess (响应结果) {String} orderApplyInfoDto.isFistAckFlag 是否首次实缴,1:是;0:不是
     * @apiSuccess (响应结果) {Number} orderApplyInfoDto.subscribeAmt 认缴金额
     * @apiSuccess (响应结果) {Number} orderApplyInfoDto.paidAmt 实缴金额/购买金额
     * @apiSuccess (响应结果) {Number} orderApplyInfoDto.actualPayAmt 实际支付金额
     * @apiSuccess (响应结果) {String} orderApplyInfoDto.isUpdateOrder 是否修改订单,1:是修改订单,0:不是修改订单
     * @apiSuccess (响应结果) {String} orderApplyInfoDto.lessPreviousAmt 是否低于上次付款金额,1:低于上次付款金额,0:不低于上次付款金额
     * @apiSuccess (响应结果) {String} orderApplyInfoDto.noticeMsgTitle 提示语标题
     * @apiSuccess (响应结果) {String} orderApplyInfoDto.noticeMsgContent 提示语内容
     * @apiSuccess (响应结果) {Object} customerBankCardInfo 用户银行卡信息
     * @apiSuccess (响应结果) {String} customerBankCardInfo.bankCode 银行编号
     * @apiSuccess (响应结果) {String} customerBankCardInfo.bankAcctMask 银行卡号掩码
     * @apiSuccess (响应结果) {String} customerBankCardInfo.bankRegionName 分行名称
     * @apiSuccess (响应结果) {String} customerBankCardInfo.bankName 银行名称
     * @apiSuccess (响应结果) {String} customerBankCardInfo.bankLogoUrl 银行图标地址
     * @apiSuccess (响应结果) {Object} buyReceiverInfoDto 收款人信息
     * @apiSuccess (响应结果) {String} buyReceiverInfoDto.receiverAccountName 收款账户姓名
     * @apiSuccess (响应结果) {String} buyReceiverInfoDto.receiverAccountNo 收款账户号
     * @apiSuccess (响应结果) {String} buyReceiverInfoDto.receiverBankName 收款人开户行
     * @apiSuccessExample 响应结果示例
     * {"customerBankCardInfo":{"bankLogoUrl":"ce4jvgYks","bankCode":"8a6","bankAcctMask":"D","bankRegionName":"bIqisP0","bankName":"jUIkC"},"buyReceiverInfoDto":{"receiverAccountName":"zu2ju","receiverBankName":"Z4W","receiverAccountNo":"e2uQ1jMlYt"},"orderApplyInfoDto":{"isFistAckFlag":"yQel47R","fundCode":"iQ","noticeMsgTitle":"oJhOGdV9","subscribeAmt":9582.************,"lessPreviousAmt":"E91","noticeMsgContent":"81t9q","payEndDt":"WY1","peDivideCallFlag":"CwFC","paidAmt":9035.************,"actualPayAmt":8841.************,"fundName":"TM61H","isUpdateOrder":"g"}}
     */
    @RequestMapping("/simu/trade/getPayApplyResult.htm")
    public void getPayApplyResult(HttpServletRequest request, HttpServletResponse response) throws IOException {
        QueryPayApplyInfoCmd queryPayApplyInfoCmd = buildQueryBuyResultCmd();
        TradeSession loginInfo = getCustSession();
        PayApplyResultVo payApplyResultVo = buyService.getPayOrderResultInfo(queryPayApplyInfoCmd);
        AssetCertificateStatusDto assetCertificateStatusDto = super.queryAssetCertificateStatus(loginInfo.getUser().getHboneNo(), payApplyResultVo.getManagerAttribute(), queryPayApplyInfoCmd.getDisCode());
        payApplyResultVo.setVerifyStatus(assetCertificateStatusDto.getStatus());
        write(payApplyResultVo, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 构建查询购买结果入参
     */
    private QueryPayApplyInfoCmd buildQueryBuyResultCmd() {
        TradeSession loginInfo = getCustSession();
        String dealNo = getString("dealNo");
        if (StringUtils.isBlank(dealNo)) {
            throw new BusinessException(ExceptionCodes.PARAMS_ERROR, "订单号不能为空");
        }
        String distributionCode = RemoteParametersProvider.getDistributionCode();
        QueryPayApplyInfoCmd queryPayApplyInfoCmd = new QueryPayApplyInfoCmd();
        // 分销渠道
        queryPayApplyInfoCmd.setDisCode(distributionCode);
        // 一账通账号
        queryPayApplyInfoCmd.setHbOneNo(loginInfo.getUser().getHboneNo());
        // 交易账号
        String txAcctNo = loginInfo.getUser().getCustNo();
        if (StringUtils.isBlank(txAcctNo)) {
            txAcctNo = loginInfo.getUser().getTxAcctNo();
        }
        queryPayApplyInfoCmd.setTxAcctNo(txAcctNo);
        queryPayApplyInfoCmd.setDealNo(dealNo);
        return queryPayApplyInfoCmd;
    }

    /**
     * @api {POST} /simu/trade/authenticate.htm 购买手机鉴权申请
     * @apiVersion 1.0.0
     * @apiGroup buy
     * @apiName authenticate
     * @apiDescription 购买手机鉴权申请
     * @apiParam (请求参数) {String} fundCode 产品代码
     * @apiParam (请求参数) {String} mobile 手机号
     * @apiParam (请求参数) {String} oldMobile 旧手机号
     * @apiParam (请求参数) {String} paymentType 01-自划款 04-银行卡 06-储蓄罐
     * @apiParam (请求参数) {String} custBankId 银行卡编号
     * @apiParam (请求参数) {String} disCode 交易渠道
     * @apiParam (请求参数) {String} msgType  0-短信、1-语音
     * @apiParamExample 请求参数示例
     * oldMobile=I&fundCode=pPkjuM&mobile=Ebng&custBankId=ivt&disCode=UOG59A4Z&paymentType=FZWbsMY3G
     * @apiSuccess (响应结果) {String} authenticateSeriousNo 鉴权流水号
     * @apiSuccess (响应结果) {String} authenticateType 鉴权类型
     * @apiSuccess (响应结果) {String} realAuthMsgType 实际发送鉴权信息类型
     * @apiSuccessExample 响应结果示例
     * {"realAuthMsgType":"Xr","authenticateType":"ubp2Pxxu","authenticateSeriousNo":"YN"}
     */
    @RequestMapping("/simu/trade/authenticate.htm")
    public void authenticate(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String msgType = RequestUtil.getParameter("msgType");// 0-短信、1-语音
        if (msgType == null) {
            msgType = YesOrNoEnum.NO.getCode();
        }
        TradeSession loginInfo = getCustSession();
        // 解析请求request
        String disCode = RemoteParametersProvider.getDistributionCode();
        AuthenticateCmd cmd = getCommand(AuthenticateCmd.class);
        ;
        if (StringUtils.isBlank(cmd.getDisCode())) {
            cmd.setDisCode(disCode);
        }
        ValidateUtil.assertValid(cmd);
        // 查询产品基本信息
        HighProductInfoModel highProductBaseModel = buyService.getHighProductInfo(cmd.getFundCode());
        // 校验是否支持在线购买
        validWebBranchCode(highProductBaseModel.getBranchCode());
        Set<String> howBuyMobiles = getHowBuyMobile(loginInfo, cmd.getDisCode());
        // 手机号是否在好买预留
        boolean mobileExitFlag = exitMobile(howBuyMobiles, cmd.getMobile());
        String sendMsgReqId = UUID.randomUUID().toString();
        String realAuthMsgType = null;
        if ("0".equals(msgType)) {
            // 获取鉴权验证码
            realAuthMsgType = getAuthenticateCode(cmd, loginInfo, sendMsgReqId, mobileExitFlag, highProductBaseModel.getProductChannel(), cmd.getDisCode());
        } else if ("1".equals(msgType)) {
            StringBuilder authenticateKey = new StringBuilder(CacheKeyPrefix.HIGH_SEND_AUTH_CODE_PREFIX).append("BUY|").append("AUTHID|").append("|").append(sendMsgReqId);
            String verfyCode = RandomUtil.randomDigit(6, simuCcmsServiceRegister.getDigitsRank());// 随机数
            SendMsgResult sendMsgResult = sendMessageService.sendVoiceMsg(loginInfo, cmd.getMobile(), verfyCode, mobileExitFlag);
            if (sendMsgResult != null && "0".equals(String.valueOf(sendMsgResult.getCode()))) {
                // 发送成功，验证码放入缓存
                Map<String, String> authRst = new HashMap<String, String>();
                // 鉴权通道 0-好买，在验证码校验时，会根据通道选择校验方式
                authRst.put("sendMsgChannel", "0");
                // 鉴权流水
                authRst.put("authenticateSeriousNo", UUID.randomUUID().toString());
                // 好买短信验证码
                authRst.put("authCode", verfyCode);
                // 鉴权类型 2-快捷鉴权1-修改手机号3-好买 4-e签宝 5-腾讯语音
                authRst.put("authMsgType", AuthMobileVerifyFlagEnum.TENCENT_VOICE_AUTH.getCode());
                // 实际发送鉴权信息类型 2-快捷鉴权1-修改手机号3-好买 4-e签宝 5-腾讯语音
                authRst.put("realAuthMsgType", AuthMobileVerifyFlagEnum.TENCENT_VOICE_AUTH.getCode());
                cacheService.put(authenticateKey.toString(), JSON.toJSONString(authRst));
            } else {
                // 发送失败，抛出异常
                log.error("SendMessageService|sendVoiceMsg|sendTencentVoiceMessage err, sendMsgResult:{}", sendMsgResult);
                throw new BizException(ExceptionCodes.HIGH_ORDER_SYSTEM_ERROR, "发生语音验证码失败");
            }
            // 腾讯语音鉴权
            realAuthMsgType = AuthMobileVerifyFlagEnum.TENCENT_VOICE_AUTH.getCode();
        } else {
            throw new BizException(ExceptionCodes.HIGH_ORDER_PARAMS_ERROR,
                    "鉴权信息类型非法");
        }
        // 返回对象
        AuthenticateDto dto = new AuthenticateDto();
        dto.setAuthenticateSeriousNo(sendMsgReqId);
        dto.setAuthenticateType("");
        dto.setRealAuthMsgType(realAuthMsgType);
        write(dto, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * validWebBranchCode:校验是否支持在线购买
     *
     * @param branchCode
     * <AUTHOR>
     * @date 2018年11月1日 上午10:07:49
     */
    private void validWebBranchCode(String branchCode) {
        if (!(BranchCodeEnum.UN_COUNTER.getCode().equals(branchCode) || BranchCodeEnum.ALL.getCode().equals(branchCode))) {
            LOG.error("BuyController|authenticate|this high product not support online buy, branchCode:{}", branchCode);
            throw new BizException("0003", "该产品不支持在线购买");
        }
    }

    /**
     * 获取支付银行卡信息
     *
     * @param txAcctNo
     * @param cmd
     * @param productChannel
     * @return com.howbuy.cgi.trade.simu.model.dto.PayDetailDto
     * @author: huaqiang.liu
     * @date: 2021/5/25 16:12
     * @since JDK 1.8
     */
    private PayDetailDto getPayDetailDto(String txAcctNo, BuySubmitMergeCmd cmd, String productChannel) {
        BuySubmitMergeCmd.PayBean payCmd = null;
        for (BuySubmitMergeCmd.PayBean pay : cmd.getPayList()) {
            if (YesOrNoEnum.YES.getCode().equals(pay.getAuthFlag())) {
                payCmd = pay;
            }
        }
        return getPayDetailDto(txAcctNo, payCmd.getCustBankId(), payCmd.getPaymentType(), productChannel);
    }

    /**
     * getPayDetailDto:获取支付银行卡信息
     *
     * @param txAcctNo
     * @param custBankId
     * @param paymentType
     * @param productChannel
     * @return
     * <AUTHOR>
     * @date 2018年11月1日 上午9:52:56
     */
    private PayDetailDto getPayDetailDto(String txAcctNo, String custBankId, String paymentType, String productChannel) {
        PayDetailDto dto = null;
        if (PaymentTypeEnum.PIGGY.getCode().equals(paymentType)) {
            // 查询储蓄罐支持随付储蓄罐份额
            List<SavingBoxVolDetail> piggyBankCardList = piggyTradeService.queryVolDetailByCustBankId(txAcctNo, "1");
            for (SavingBoxVolDetail savingBoxVolDetail : piggyBankCardList) {
                if (custBankId.equals(savingBoxVolDetail.getCustBankId())) {
                    dto = new PayDetailDto();
                    dto.setCustBankId(savingBoxVolDetail.getCustBankId());
                    dto.setBankAcct(savingBoxVolDetail.getBankAcct());
                    dto.setBankCode(savingBoxVolDetail.getBankCode());
                    // 若卡号脱敏查询明文卡号
                    if (StringUtils.isBlank(savingBoxVolDetail.getBankAcct()) || savingBoxVolDetail.getBankAcct().contains("*")) {
                        BankAcctSensitiveModel bankAcctSensitiveModel = accPlaintextService.queryBankAcctSensitiveInfo(txAcctNo, custBankId);
                        savingBoxVolDetail.setBankAcct(bankAcctSensitiveModel.getBankAcct());
                    }
                    break;
                }
            }
        } else if ((PaymentTypeEnum.AGENT_DRAWING.getCode().equals(paymentType) || PaymentTypeEnum.SELF_DRAWING.getCode().equals(paymentType))) {
            List<CustBankModel> bankingCardInfos = null;
            // 走私募的渠道
            String disCode = RemoteParametersProvider.getDistributionCode();
            if (ProductChannelEnum.TP_SM.getCode().equals(productChannel)) {
                bankingCardInfos = bankCardService.queryBindBankCardsWithDisCode(txAcctNo, disCode, "102");
            } else {
                bankingCardInfos = bankCardService.queryBindBankCardsWithDisCode(txAcctNo, disCode, null);
            }
            log.info("查询银行卡信息列表结果,bankingCardInfos={},txAcctNo={},disCode={}", JSON.toJSONString(bankingCardInfos), txAcctNo, disCode);
            if (null != bankingCardInfos && !bankingCardInfos.isEmpty()) {
                for (CustBankModel bankCardInfo : bankingCardInfos) {
                    if (custBankId.equals(bankCardInfo.getCustBankId())) {
                        dto = new PayDetailDto();
                        dto.setBankCode(bankCardInfo.getBankCode());
                        dto.setBankAcct(bankCardInfo.getBankAcct());
                        dto.setCustBankId(bankCardInfo.getCustBankId());
                        dto.setPaySign(bankCardInfo.getPaySign());
                        dto.setSingleMinLimitAmount(bankCardInfo.getSingleMinLimitAmount());
                        dto.setSingleLimitAmount(bankCardInfo.getSingleLimitAmount());
                        break;
                    }
                }
            }
        }
        if (null == dto) {
            log.error("getPayDetailDto-匹配不到合适的银行卡");
            throw new BizException(BizErrorEnum.REQUEST_PARAM_ERROR.getCode(), "匹配不到合适的银行卡");
        }
        return dto;
    }

    /**
     * @api {POST} /simu/trade/validtxpwd.htm 校验交易密码
     * @apiVersion 1.0.0
     * @apiGroup buy
     * @apiName validTxPwd
     * @apiDescription 校验交易密码
     * @apiParam (请求参数) {String} txPassword 密码
     * @apiSuccess (响应结果) {Boolean} validRst 校验结果
     * @apiSuccessExample 响应结果示例
     * {"validRst":false}
     */
    @RequestMapping("/simu/trade/validtxpwd.htm")
    public void validTxPwd(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = getCustSession();
        String txPassword = getString("txPassword");
        String disCode = RemoteParametersProvider.getDistributionCode();

        boolean validRst = validateTxPassword(loginInfo.getUser().getTxAcctNo(), EncryptUtil.getInstance(EncryptAlgEnum.Des).encMsg(txPassword.getBytes(StandardCharsets.UTF_8)), disCode);
        ValidateTxPwdDto dto = new ValidateTxPwdDto();
        dto.setValidRst(validRst);
        write(dto, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 获取到KYC相关信息
     *
     * @param hboneNo
     * @return
     */
    private KycInfoResponse getKycInfo(String hboneNo) {
        KycInfoRequest kycInfoRequest = new KycInfoRequest();
        kycInfoRequest.setHboneNo(hboneNo);
        DisCodeInvokerUtils.setCommonParameters(kycInfoRequest);
        KycInfoResponse kycInfoResponse = kycInfoFacade.execute(kycInfoRequest);

        if (null != kycInfoResponse && "0000000".equals(kycInfoResponse.getReturnCode())) {
            return kycInfoResponse;
        } else {
            return new KycInfoResponse();
        }
    }

    /**
     * getCachePre:(获取缓存中购买预约信息)
     *
     * @param buyPreKey
     * <AUTHOR>
     * @date 2018年1月26日 上午10:29:45
     */
    private PrebookDetailDto getCacheBuyPre(String buyPreKey) {
        // 获取缓存可使用预约信息
        String buyPreStr = cacheService.get(buyPreKey);

        if (StringUtils.isEmpty(buyPreStr)) {
            throw new BizException(BizErrorEnum.QUERY_CACHE_PREINFO_IS_NULL);
        }

        PrebookDetailDto prebookDetailDto = JSON.parseObject(buyPreStr, PrebookDetailDto.class);

        return prebookDetailDto;
    }

    /**
     * getSetCacheBuyPre:如果缓存查不到重新获取
     *
     * @param buyPreKey
     * @param request
     * @param loginInfo
     * @param productCode
     * @return
     * <AUTHOR>
     * @date 2018年11月16日 上午10:59:51
     */
    private PrebookDetailDto getSetCacheBuyPre(String buyPreKey, HttpServletRequest request, TradeSession loginInfo, String productCode) {
        PrebookDetailDto prebookDetailDto = null;
        // 获取缓存可使用预约信息
        String buyPreStr = cacheService.get(buyPreKey);
        if (!StringUtils.isEmpty(buyPreStr)) {
            prebookDetailDto = JSON.parseObject(buyPreStr, PrebookDetailDto.class);
        } else {
            prebookDetailDto = buyService.getCurrentPreInfo(request, loginInfo, productCode);
        }

        return prebookDetailDto;
    }


    /**
     * validateTxPassword:(校验交易密码)
     *
     * @param txAcctNo 交易账号
     * @param txPwd    交易密码
     * @return
     * <AUTHOR>
     * @date 2017年12月19日 下午4:35:59
     */
    boolean validateTxPassword(String txAcctNo, String txPwd, String disCode) {
        ValidateTxPasswordRequest request = new ValidateTxPasswordRequest();
        request.setTxAcctNo(txAcctNo);
        request.setTxPassword(txPwd);
        request.setDisCode(disCode);
        ValidateTxPasswordResponse response = validateTxPasswordFacade.execute(request);

        return YesOrNoEnum.YES.getCode().equals(response.getCheckFlag());
    }


    /**
     * 判断支付金额是否大于银行卡单笔限额
     *
     * @param cmd
     * @param payDto
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/5/25 16:38
     * @since JDK 1.8
     */
    private void validPayAmtLimit(BuySubmitMergeCmd cmd, PayDetailDto payDto) {
        for (BuySubmitMergeCmd.PayBean pay : cmd.getPayList()) {
            validPayAmtLimit(pay.getPaymentType(), payDto, pay.getPayAmount());
        }
    }

    /**
     * validPayAmtLimit:判断支付金额是否大于银行卡单笔限额
     *
     * @param paymentType
     * @param payDto
     * @param payAmt
     * <AUTHOR>
     * @date 2018年11月1日 下午9:10:23
     */
    private void validPayAmtLimit(String paymentType, PayDetailDto payDto, BigDecimal payAmt) {
        LOG.info("validPayAmtLimit-判断支付金额是否大于银行卡单笔限额,paymentType={},payDto={},payAmt={}", paymentType, JSON.toJSONString(payDto), payAmt);
        if (PaymentTypeEnum.AGENT_DRAWING.getCode().equals(paymentType)) {
            // 判断支付金额是否大于银行卡单笔限额
            boolean payLimitFlag = PaySignEnum.OPEN.getCode().equals(payDto.getPaySign()) && payAmt.compareTo(payDto.getSingleMinLimitAmount()) >= 0 && payAmt.compareTo(payDto.getSingleLimitAmount()) <= 0;
            if (!payLimitFlag) {
                // 代扣支付金额超过银行卡单笔限额
                throw new BizException(BizErrorEnum.TRADE_PURCHASE_MONEY_MORE_THAN_BANK_ACC_LIMIT);
            }
        }
    }

    /**
     * validateAuthCode:(校验短信)
     *
     * @param cmd
     * @param payDto
     * @param loginInfo
     * @param authenticateCode
     * @param reqId
     * @return
     * <AUTHOR>
     * @date 2018年8月30日 下午5:35:21
     */
    private void validateAuthCode(BuySubmitCmd cmd, PayDetailDto payDto, TradeSession loginInfo, String authenticateCode, String reqId) {
        BuySubmitMergeCmd mergeCmd = new BuySubmitMergeCmd();
        BeanUtils.copyProperties(cmd, mergeCmd);
        List<BuySubmitMergeCmd.PayBean> payList = new ArrayList<>(1);
        BuySubmitMergeCmd.PayBean pay = new BuySubmitMergeCmd.PayBean();
        BeanUtils.copyProperties(cmd, pay);
        payList.add(pay);
        mergeCmd.setPayList(payList);
        validateAuthCode(mergeCmd, payDto, loginInfo.getUser(), authenticateCode, reqId);
    }

    /**
     * validateAuthCode:(校验短信)
     *
     * @param cmd
     * @param payDto
     * @param user
     * @param authenticateCode
     * @param reqId
     * @return
     * <AUTHOR>
     * @date 2018年8月30日 下午5:35:21
     */
    private void validateAuthCode(BuySubmitMergeCmd cmd, PayDetailDto payDto, UserInfo user, String authenticateCode, String reqId) {
        // 缓存key
        String authCacheKey = CacheKeyUtil.getBuyAuthIdKey(reqId);
        // {"sendMsgChannel", SEND_MSG_CHANNEL_PAY,"authenticateSeriousNo", "", "authCode", "","authMsgType":""};
        String authRst = cacheService.get(authCacheKey);
        LOG.info("validateAuthCode|authRst：{}", authRst);
        if (authRst == null) {
            throw new BizException(BizErrorEnum.AUTHENTICATE_CODE_ERROR);
        }
        JSONObject jsonObject = JSON.parseObject(authRst);
        // 鉴权渠道
        String sendMsgChannel = jsonObject.get("sendMsgChannel").toString();
        // 实际验证码
        String authCode = jsonObject.get("authCode").toString();
        // 鉴权方式 2-快捷鉴权 1-修改手机号 3-好买4-e签宝
        String authMoblieVerifyFlag = jsonObject.get("authMsgType").toString();
        // 鉴权流水
        String authenticateSeriousNo = jsonObject.get("authenticateSeriousNo").toString();
        // 验证码鉴权结果
        boolean authenticateFlag = false;
        if (StringUtils.isNotBlank(authenticateCode)) {
            authenticateFlag = isAuthenticateFlag(cmd, payDto, user, authenticateCode, sendMsgChannel, authCode, authMoblieVerifyFlag, authenticateSeriousNo, authenticateFlag);
        }
        if (!authenticateFlag) {
            throw new BizException(BizErrorEnum.AUTHENTICATE_CODE_ERROR);
        }

        // 清除缓存
        cacheService.remove(authCacheKey);
    }

    private boolean isAuthenticateFlag(BuySubmitMergeCmd cmd, PayDetailDto payDto, UserInfo user, String authenticateCode, String sendMsgChannel, String authCode, String authMoblieVerifyFlag, String authenticateSeriousNo, boolean authenticateFlag) {
        if (CGISimuConstants.SEND_MSG_CHANNEL_ES.equals(sendMsgChannel)) {
            // e签宝鉴权
            CheckMobileVerificationCodeRequest checkReq = new CheckMobileVerificationCodeRequest();
            checkReq.setIdNo(user.getIdNo());
            checkReq.setIdType(user.getIdType());
            checkReq.setName(user.getCustName());
            checkReq.setCode(cmd.getAuthenticateCode());
            checkReq.setMobile(cmd.getMobile());
            EsResponse checkMobileVerificationCodeResp = checkMobileVerificationCodeFacade.execute(checkReq);
            if (checkMobileVerificationCodeResp != null && ES_VALIDATE_CODE_SUCC.equals(checkMobileVerificationCodeResp.getRetCode())) {
                authenticateFlag = true;
            }

        } else if (CGISimuConstants.SEND_MSG_CHANNEL_PAY.equals(sendMsgChannel)) {
            // 支付机构短信校验
            log.info("buyInfo.getAuthMoblieVerifyFlag() :" + authMoblieVerifyFlag);
            if (AuthMobileVerifyFlagEnum.QUICK_AUTH.getCode().equals(authMoblieVerifyFlag)) {
                String serialNo = UUID.randomUUID().toString();
                QuickCardAuthRsltRequest quickCardAuthRsltRequest = new QuickCardAuthRsltRequest();
                quickCardAuthRsltRequest.setApplyDealNo(authenticateSeriousNo);
                quickCardAuthRsltRequest.setDealNo(serialNo);
                quickCardAuthRsltRequest.setDealDate(new Date());
                quickCardAuthRsltRequest.setCaptcha(authenticateCode);
                QuickCardAuthRsltResponse quickCardAuthRsltResponse = quickCardAuthRsltFacade.execute(quickCardAuthRsltRequest);
                if (null != quickCardAuthRsltResponse && "0000000".equals(quickCardAuthRsltResponse.getReturnCode())
                        && "00".equals(quickCardAuthRsltResponse.getAuthState().toString())) {
                    authenticateFlag = true;
                    log.info("{} 快捷鉴权成功（支付）", user.getCustName());
                } else {
                    log.info("{} 快捷鉴权失败（支付）", user.getCustName());
                }
            } else if (AuthMobileVerifyFlagEnum.EMPTY_OLDMOBILE.getCode().equals(authMoblieVerifyFlag)
                    || AuthMobileVerifyFlagEnum.MODIFY_MOBILE.getCode().equals(authMoblieVerifyFlag)) {
                ConvenientConfirmForMobileRequest convenientConfirmForMobileRequest = new ConvenientConfirmForMobileRequest();
                convenientConfirmForMobileRequest.setTxAcctNo(user.getTxAcctNo());
                convenientConfirmForMobileRequest.setBankAcctDigest(DigestUtil.digest(payDto.getBankAcct()));
                convenientConfirmForMobileRequest.setMobileNo(cmd.getMobile());
                convenientConfirmForMobileRequest.setValidationCode(authenticateCode);
                convenientConfirmForMobileRequest.setConvenientAppContractNo(authenticateSeriousNo);
                ConvenientConfirmForMobileResponse convenientConfirmForMobileResponse = convenientConfirmForMobileFacade
                        .execute(convenientConfirmForMobileRequest);
                if (null != convenientConfirmForMobileResponse && "0000000".equals(convenientConfirmForMobileResponse.getReturnCode())
                        && VrfyStatEnum.PASS.getCode().equals(convenientConfirmForMobileResponse.getVrfyStat())) {
                    authenticateFlag = true;
                    log.info("{} 鉴权成功（账户中心）", user.getCustName());
                } else {
                    log.info("{} 鉴权失败（账户中心）", user.getCustName());
                }
            }
        } else if (CGISimuConstants.SEND_MSG_CHANNEL_HOWBUY.equals(sendMsgChannel)) {
            // 好买短信校验
            log.info("真实验证码：{},实际验证码：{}", authCode, authenticateCode);
            if (authenticateCode.equals(authCode)) {
                authenticateFlag = true;
                log.info("验证码验证成功");
            }
        }
        return authenticateFlag;
    }


    /**
     * exitMobile:(判断手机号是否属于用户在好买绑定的手机号)
     *
     * @param mobile
     * @return
     * <AUTHOR>
     * @date 2017年9月26日 下午5:42:20
     */
    private boolean exitMobile(Set<String> howbuyMobiles, String mobile) {
        LOG.info("mobile {} is exit ", mobile);
        return howbuyMobiles.contains(mobile);
    }

    private Set<String> getHowBuyMobile(TradeSession loginInfo, String disCode) {
        Set<String> mobiles = new HashSet<>();
        if (!StringUtils.isEmpty(loginInfo.getUser().getMobile())) {
            mobiles.add(loginInfo.getUser().getMobile());
        }

        CustMobileModel custMobileModel = accPlaintextService.queryCustMobile(null, loginInfo.getUser().getHboneNo(), disCode);
        // 一帐通手机号
        if (!StringUtils.isEmpty(custMobileModel.getMobile())) {
            mobiles.add(custMobileModel.getMobile());
        }

        // 银行手机号
        if (custMobileModel.getBankMobileMap() != null) {
            for (String bankMobile : custMobileModel.getBankMobileMap().values()) {
                if (!StringUtils.isEmpty(bankMobile)) {
                    mobiles.add(bankMobile);
                }
            }
        }

        return mobiles;

    }

    /**
     * 获取好买、银行预留手机号
     *
     * @param loginInfo
     * @return
     */
    private Map<String, String> getHowbuyAndBankMobile(TradeSession loginInfo) {
        Map<String, String> map = new HashMap<>();
        CustMobileModel custMobileModel = accPlaintextService.queryCustMobile(null, loginInfo.getUser().getHboneNo());
        // 一帐通手机号
        if (!StringUtils.isEmpty(custMobileModel.getMobile())) {
            String howbuyMobile = custMobileModel.getMobile();
            map.put("howbuyMobile", howbuyMobile);
        }
        // 银行手机号，经管月武确认，一个资金账号只会有一个手机号
        if (custMobileModel.getBankMobileMap() != null) {
            for (String mobile : custMobileModel.getBankMobileMap().values()) {
                if (!StringUtils.isEmpty(mobile)) {
                    String bankMobile = mobile;
                    map.put("bankMobile", bankMobile);
                    break;
                }
            }
        }
        return map;
    }

    /**
     * getAuthMoblieVerifyFlag: 快捷鉴权or修改手机号
     *
     * @param oldMobile
     * @param mobile
     * @return
     * <AUTHOR>
     * @date 2018年10月31日 下午9:01:13
     */
    private String getAuthMoblieVerifyFlag(String oldMobile, String mobile) {
        if (null == oldMobile || oldMobile.length() == 0) {
            return "0";
        } else {
            if (oldMobile.equals(mobile)) {
                return "2"; // 快捷鉴权
            } else {
                return "1";// 修改手机号
            }
        }
    }

    /**
     * getAuthenticateCode:(获取验证码)
     *
     * @param cmd
     * @param loginInfo
     * @param sendMsgReqId
     * @param mobileExit
     * @param productChannel
     * @return realAuthMsgType 实际发送鉴权信息类型
     * <AUTHOR>
     * @date 2018年8月30日 下午5:50:25
     */
    private String getAuthenticateCode(AuthenticateCmd cmd, TradeSession loginInfo, String sendMsgReqId, boolean mobileExit, String productChannel, String disCode) {
        LOG.info("GenerateAuthCodeTask|getAuthenticateCode|params|cmd:{}, loginInfo:{}, " + "reqId:{}", JSON.toJSONString(cmd),
                JSON.toJSONString(loginInfo), JSON.toJSONString(sendMsgReqId));
        // 为了高端自动化下单,如果有标签,需要将缓存设置固定值
        if (StringUtils.isNotBlank(simuCcmsServiceRegister.getMobileAuthMsgUseCfg()) && YesOrNoEnum.YES.getCode().equals(simuCcmsServiceRegister.getMobileAuthMsgUseCfg())) {
            log.info("getAuthenticateCode-不校验开关生效,使用固定验签");
            sendMsgReqId = "UUID123456";
            Map<String, String> authRst = new HashMap<String, String>();
            // 鉴权通道
            authRst.put("sendMsgChannel", "0");
            // 鉴权流水
            authRst.put("authenticateSeriousNo", sendMsgReqId);
            // 好买短信验证码
            authRst.put("authCode", "123456");
            // 鉴权类型 -好买
            authRst.put("authMsgType", AuthMobileVerifyFlagEnum.HOWBUY_AUTH.getCode());
            // 实际发送鉴权信息类型
            authRst.put("realAuthMsgType", AuthMobileVerifyFlagEnum.HOWBUY_AUTH.getCode());
            cacheService.put(CacheKeyPrefix.HIGH_SEND_AUTH_CODE_PREFIX + "BUY|" + "AUTHID|" + "|" + sendMsgReqId
                    // 鉴权通道
                    // 鉴权流水
                    // 好买短信验证码
                    // 鉴权类型 -好买
                    // 实际发送鉴权信息类型
                    , JSON.toJSONString(authRst));
            return AuthMobileVerifyFlagEnum.HOWBUY_AUTH.getCode();
        }
        String txAcctNo = loginInfo.getUser().getCustNo();
        SendAuthMsgRequest sendAuthMsgRequest = new SendAuthMsgRequest();
        // 发送鉴权信息类型 // 2-快捷鉴权 // 1-修改手机号 3-好买 4-e签宝
        String authMsgType = getAuthMoblieVerifyFlag(cmd.getOldMobile(), cmd.getMobile());
        // 交易账号
        sendAuthMsgRequest.setTxAcctNo(txAcctNo);
        // 发送鉴权信息类型
        sendAuthMsgRequest.setAuthMsgType(authMsgType);
        //发送鉴权信息标识id
        sendAuthMsgRequest.setSendMsgReqId(sendMsgReqId);
        // 外部订单单号
        sendAuthMsgRequest.setExternalDealNo(sendMsgReqId);
        sendAuthMsgRequest.setMobileExit(mobileExit);
        sendAuthMsgRequest.setIdType(loginInfo.getUser().getIdType());
        sendAuthMsgRequest.setMobile(cmd.getMobile());
        sendAuthMsgRequest.setCustName(loginInfo.getUser().getCustName());
        sendAuthMsgRequest.setIdNo(loginInfo.getUser().getIdNo());
        // 好买发送鉴权信息参数
        MessageCenterConterxtBean messageCenterConterxtBean = new MessageCenterConterxtBean();
        // 随机数
        String verfyCode = RandomUtil.randomDigit(6, simuCcmsServiceRegister.getDigitsRank());
        // 短信模板
        if (DisCodeEnum.HZ.getCode().equals(disCode)) {
            messageCenterConterxtBean.setBusinessId(CGISimuConstants.HZ_BUSINESSID);
        } else {
            messageCenterConterxtBean.setBusinessId(CGISimuConstants.BUSINESSID);
        }
        // 客户号
        messageCenterConterxtBean.setCustNo(txAcctNo);
        // 手机号
        messageCenterConterxtBean.setMobile(cmd.getMobile());
        // 验证码
        messageCenterConterxtBean.setRandom(verfyCode);
        // 客户类型
        messageCenterConterxtBean.setCustType(CGISimuConstants.CUST_TYPE_EHOWBUY);

        sendAuthMsgRequest.setMessageCenterConterxtBean(messageCenterConterxtBean);
        if (DisCodeEnum.HZ.getCode().equals(disCode)) {
            // 好买短信
            sendAuthMsgRequest.setAuthMsgType(AuthMobileVerifyFlagEnum.HOWBUY_AUTH.getCode());
        } else {
            // 支付银行卡信息
            PayDetailDto payDto = getPayDetailDto(txAcctNo, cmd.getCustBankId(), cmd.getPaymentType(), productChannel);
            // 支付银行
            sendAuthMsgRequest.setBankCode(payDto.getBankCode());
            // 获取银行对应短信鉴权方式
            String bankAuthType = getBankAuthType(payDto.getBankCode());
            getHmAuthMsgType(cmd, loginInfo, txAcctNo, payDto, bankAuthType, sendAuthMsgRequest, authMsgType);
        }
        SendAuthMsgResponse sendAuthMsgResponse = sendAuthMsgFacade.execute(sendAuthMsgRequest);
        if (sendAuthMsgResponse != null && !ReturnCodeEnum.SUCC_TMS.getCode().equals(sendAuthMsgResponse.getReturnCode())) {
            throw new BizException(sendAuthMsgResponse.getReturnCode(), sendAuthMsgResponse.getDescription());
        }

        // 获取实际发送鉴权信息类型
        String authRstStr = null;
        try {
            authRstStr = cacheService.get(CacheKeyPrefix.HIGH_SEND_AUTH_CODE_PREFIX + "BUY|" + "AUTHID|" + "|" + sendMsgReqId);
        } catch (Exception e) {
            LOG.error("BuyController|authenticate|get authRstStr err :", e);
        }
        LOG.info("BuyController|authenticate|authRstStr：{}", authRstStr);
        if (authRstStr != null) {
            JSONObject jsonObject = JSON.parseObject(authRstStr);
            // 实际发送鉴权信息类型
            Object realAuthMsgType = jsonObject.get("realAuthMsgType");
            if (realAuthMsgType != null && StringUtils.isNotEmpty(realAuthMsgType.toString())) {
                return realAuthMsgType.toString();
            } else {
                return sendAuthMsgRequest.getAuthMsgType();
            }
        } else {
            return sendAuthMsgRequest.getAuthMsgType();
        }
    }


    /**
     * @api {GET} /simu/trade/fundBuyStatus.htm fundBuyStatus
     * @apiVersion 1.0.0
     * @apiGroup BuyController
     * @apiName fundBuyStatus
     * @apiDescription 获取产品购买状态
     * @apiParam {String} fundCode 产品编码
     * @apiSuccess (响应结果) {String} productCode 产品代码
     * @apiSuccess (响应结果) {String} shareClass 收费类型A-前收费;B-后收费
     * @apiSuccess (响应结果) {String} buyStatus 产品状态0-不可购买 1-可购买
     * @apiSuccess (响应结果) {String} fundBuyStatus 产品购买状态,CAN_BUY:可购买,CAN_NOT_BUY:不可购买,CAN_MODIFY:可修改,CAN_NOT_MODIFY:不可修改;
     * @apiSuccess (响应结果) {String} buyStatusType 状态标识1-正常 2-代销不支持 3.年龄限制 4-已售罄 5-直销转代销的黑名单6-产品状态不可购买或不在预约期内          8-产品参数配置有误99-其它
     * @apiSuccess (响应结果) {String} msg 说明信息
     * @apiSuccessExample 响应结果示例
     * {"msg":"16ZzAf","productCode":"VrV5C20","shareClass":"ViuUm","buyStatus":"Nm4","buyStatusType":"V","fundBuyStatus":"bPeVhvVzT"}
     */
    @RequestMapping("/simu/trade/fundBuyStatus.htm")
    public void fundBuyStatus(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = this.getCustSession();
        String fundCode = getString("fundCode");
        if (StringUtils.isBlank(fundCode)) {
            throw new BizException(BizErrorEnum.REQUEST_PARAMS_ERROR.getCode(), "产品编码不能为空");
        }
        BuyFundStatusBean fundBuyStatus = buyService.getFundBuyStatus(request, fundCode, loginInfo.getUser().getTxAcctNo());
        write(fundBuyStatus, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * @api {GET} /simu/trade/queryriskhintinfo.htm queryRiskHintInfo()
     * @apiVersion 1.0.0
     * @apiGroup BuyController
     * @apiName queryRiskHintInfo()
     * @apiDescription 查询风险提示信息接口
     * @apiParam (请求参数) {String} disCode 分销渠道
     * @apiSuccess (响应结果) {String} riskToleranceLevel 风险评测等级 风险等级 0,1,2,3,4,5 没有做过的情况下为null
     * @apiSuccess (响应结果) {String} riskToleranceDate 风险评测日期 yyyyMMdd
     * @apiSuccess (响应结果) {String} riskHintConfirmDtm 风测提醒确定时间 yyyyMMddHHmmss
     * @apiSuccess (响应结果) {String} investorType 投资者类型,专业:PRO;普通:NORMAL
     * @apiSuccess (响应结果) {String} investorQualifiedDate 投资者类型认证日期 YYYYMMDD
     * @apiSuccess (响应结果) {String} investorQualifiedHintConfirmDtm 投资者类型认证提醒确认时间 yyyyMMddHHmmss
     * @apiSuccessExample 响应结果示例
     * {"riskToleranceLevel":"lbzVajY6q","riskToleranceDate":"6nvd0ZthUY","riskConfirmDateTime":"2mt"}
     */
    @RequestMapping("/simu/trade/queryriskhintinfo.htm")
    public void queryRiskHintInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        TradeSession loginInfo = this.getCustSession();
        String disCode = getString("disCode");
        if (StringUtils.isBlank(disCode)) {
            throw new BizException(BizErrorEnum.REQUEST_PARAMS_ERROR.getCode(), "分销渠道不能为空");
        }
        RiskHintInfoVo riskHintInfoVo = new RiskHintInfoVo();
        // 1.查询合规信息
        QueryComplianceStateDto complianceStateDto = accCenterService.queryQueryComplianceState(loginInfo.getUser().getHboneNo(), disCode);
        // 2.赋值
        riskHintInfoVo.setRiskToleranceLevel(complianceStateDto.getRiskToleranceLevel());
        riskHintInfoVo.setRiskToleranceDate(complianceStateDto.getRiskToleranceDate());
        riskHintInfoVo.setRiskHintConfirmDtm(DateUtils.getCurrentTime());
        riskHintInfoVo.setInvestorType(complianceStateDto.getInvestorType());
        riskHintInfoVo.setInvestorQualifiedDate(complianceStateDto.getInvestorQualifiedDate());
        riskHintInfoVo.setInvestorQualifiedHintConfirmDtm(DateUtils.getCurrentTime());

        write(riskHintInfoVo, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }


    /**
     * 渠道为好买,发送短信
     */
    private void getHmAuthMsgType(AuthenticateCmd cmd, TradeSession loginInfo, String txAcctNo, PayDetailDto payDto, String bankAuthType, SendAuthMsgRequest sendAuthMsgRequest, String authMsgType) {
        // 获取好买、银行预留手机号
        Map<String, String> mobileMap = getHowbuyAndBankMobile(loginInfo);
        // 非身份证开户的用户，发送验证码的通道使用好买通道
        if (!IdTypeEnum.IDCARD.getValue().equals(loginInfo.getUser().getIdType())) {
            // 好买短信
            sendAuthMsgRequest.setAuthMsgType(AuthMobileVerifyFlagEnum.HOWBUY_AUTH.getCode());
        }
        // 当前手机号 不是 银行预留手机号，发送验证码的通道使用好买通道
        else if (!cmd.getMobile().equals(mobileMap.get("bankMobile"))) {
            // 好买短信
            sendAuthMsgRequest.setAuthMsgType(AuthMobileVerifyFlagEnum.HOWBUY_AUTH.getCode());
        }
        // 当前手机号 是 银行预留手机号，但银行短信鉴权方式属于“好买通道”，发送验证码的通道使用好买
        else if (CGISimuConstants.SEND_MSG_CHANNEL_HOWBUY.equals(bankAuthType)) {
            // 好买短信
            sendAuthMsgRequest.setAuthMsgType(AuthMobileVerifyFlagEnum.HOWBUY_AUTH.getCode());
        }
        // 当前手机号 是 银行预留手机号，但银行短信鉴权方式属于“e签宝”，发送验证码的通道使用e签宝
        else if (CGISimuConstants.SEND_MSG_CHANNEL_ES.equals(bankAuthType)) {
            // e签宝
            sendAuthMsgRequest.setAuthMsgType(AuthMobileVerifyFlagEnum.E_AUTH.getCode());
        }
        // 当前手机号 是 银行预留手机号，但银行短信鉴权方式不属于“e签宝”、不属于“好买通道”，发送验证码的通道使用支付渠
        else {
            if (AuthMobileVerifyFlagEnum.QUICK_AUTH.getCode().equals(authMsgType)) {
                QuickCardAuthContextBean quickCardAuthContextBean = new QuickCardAuthContextBean();//快捷鉴权参数
                quickCardAuthContextBean.setAuthType(BusiCodeEnum.AUTH_RULA.name());
                quickCardAuthContextBean.setDealDate(new Date());
                quickCardAuthContextBean.setDealNo(UUID.randomUUID().toString());
                quickCardAuthContextBean.setTxAcctNo(loginInfo.getUser().getTxAcctNo());
                quickCardAuthContextBean.setMobile(cmd.getMobile());
                quickCardAuthContextBean.setCustName(loginInfo.getUser().getCustName());
                quickCardAuthContextBean.setIdNo(loginInfo.getUser().getIdNo());
                quickCardAuthContextBean.setIdType(loginInfo.getUser().getIdType());
                quickCardAuthContextBean.setBankAcct(payDto.getBankAcct());
                BankAcctSensitiveModel model = accPlaintextService.queryBankAcctSensitiveInfo(txAcctNo, cmd.getCustBankId());
                //设置明文银行卡号
                if (model != null && StringUtils.isNotEmpty(model.getBankAcct())) {
                    quickCardAuthContextBean.setBankAcct(model.getBankAcct());
                }
                quickCardAuthContextBean.setBankCode(payDto.getBankCode());
                sendAuthMsgRequest.setQuickCardAuthContextBean(quickCardAuthContextBean);
            } else {
                ConvenientVrifyForMobileBean convenientVrifyForMobileBean = new ConvenientVrifyForMobileBean();//修改银行预留手机号
                convenientVrifyForMobileBean.setTxAcctNo(loginInfo.getUser().getTxAcctNo());
                convenientVrifyForMobileBean.setBankAcct(payDto.getBankAcct());
                convenientVrifyForMobileBean.setVerifyFlag(authMsgType);
                convenientVrifyForMobileBean.setOldMobileNo(cmd.getOldMobile());
                convenientVrifyForMobileBean.setMobileNo(cmd.getMobile());
                sendAuthMsgRequest.setConvenientVrifyForMobileBean(convenientVrifyForMobileBean);
            }
        }
    }


    // 构建购买返回参数
    private BuyDto buildBuyDto(KycInfoResponse kyc, QuerySuppleStatusResponse supple, HighProductAppointmentInfoModel appoint, HighProductInfoModel product,
                               HighProductLimitModel limit, List<HighProductFeeRateModel> feeList, HighProductActiDiscountListModel acti, PrebookDetailDto prebookDetailDto, BigDecimal leftAmt, String hboneBindMobile) {
        BuyDto dto = new BuyDto();
        // 客户风险等级，投资者类型（专业or普通）
        dto.setKycResponse(setOutKycInfo(kyc));
        // 追加购买状态: 1-首次购买, 2-追加
        dto.setIsBought(supple.getSuppleSubsStatus());
        // 1-真首单; 2-伪首单
        dto.setIsHasNopayAmt(supple.getFirstOrderDes());
        if (null != appoint) {
            // 开放开始日期
            dto.setOpenStartDt(appoint.getOpenStartDt());
            // 开放结束日期
            dto.setOpenEndDt(appoint.getOpenEndDt());
            // 预约开始日期
            dto.setAppointmentStartDt(appoint.getAppointStartDt());
            // 预约结束日期
            dto.setAppointmentEndDt(appoint.getApponitEndDt());
            // 打款截止日期
            dto.setPayEndDate(appoint.getPayDeadlineDtm());
        }
        // 收费类型A-前收费
        dto.setShareClass(product.getShareClass());
        // 产品通道3-群济私募,6-高端公募,7-TP私募
        dto.setProductChannel(product.getProductChannel());
        // 构建产品信息
        dto.setHighProductBaseModel(getHighProductBaseModel(product, limit, dto));
        // 费率
        dto.setFundFeeRateList(feeList);
        // 代销折扣
        dto.setAgendDisc(acti.getAgentDisc());
        // 活动折扣列表
        dto.setHighProductActiDiscountModelList(acti.getDiscountList());
        // 活动折扣
        dto.setActivityRate(acti.getDiscountList().get(0).getDiscountRatio());
        // 风险等级是否匹配0：不匹配，1：匹配 2：风险已过期
        dto.setMatchRiskLevelFlag(buyService.getMatchRiskLevelFlag(kyc, product));
        // 风险评估强制匹配默认false
        dto.setTestForceMatchFlag(buyService.getRiskForceMatchFlag(kyc, product));
        // crm预约信息
        dto.setSubsciptionVO(prebookDetailDto);
        // 剩余额度
        dto.setSurplusAmount(leftAmt);
        // 是否绑定一帐通手机号
        if (StringUtils.isEmpty(hboneBindMobile)) {
            dto.setHbOneMobileBind(com.howbuy.tms.common.enums.busi.YesOrNoEnum.NO.getCode());
        } else {
            dto.setHbOneMobileBind(com.howbuy.tms.common.enums.busi.YesOrNoEnum.YES.getCode());
        }

        // 是否绑定邮箱
        if (!StringUtils.isEmpty(simuCcmsServiceRegister.getValiateEmail()) &&
                com.howbuy.tms.common.enums.busi.YesOrNoEnum.YES.getCode().equals(simuCcmsServiceRegister.getValiateEmail())) {
            dto.setValidEmailBind(com.howbuy.tms.common.enums.busi.YesOrNoEnum.YES.getCode());
        } else {
            dto.setValidEmailBind(com.howbuy.tms.common.enums.busi.YesOrNoEnum.NO.getCode());
        }

        return dto;
    }

    private HighProductInfoDto getHighProductBaseModel(HighProductInfoModel product,
                                                       HighProductLimitModel limit,
                                                       BuyDto dto) {
        HighProductInfoDto highProductInfoDto = BusiUtil.buildProductInfo(product, limit);

        // 追加购买状态: 1-首次购买, 2-追加
        boolean firstBuyFlag = FirstBuyFlagEnum.YES.getCode().equals(dto.getIsBought());
        if (firstBuyFlag) {
            highProductInfoDto.setProdDiffer(product.getProdAppDiffer());//产品极差
        }
        return highProductInfoDto;
    }


    /**
     * getIsBought: 查询是否首单
     *
     * @param txAcctNo
     * @param fundCode
     * @param shareClass
     * @return
     * <AUTHOR>
     * @date 2018年10月30日 上午9:56:54
     */
    private QuerySuppleStatusResponse getIsBought(String txAcctNo, String fundCode, String shareClass) {
        QuerySuppleStatusRequest querySuppleStatusRequest = new QuerySuppleStatusRequest();
        querySuppleStatusRequest.setTxAcctNo(txAcctNo);
        querySuppleStatusRequest.setFundCode(fundCode);
        querySuppleStatusRequest.setFundShareClass(shareClass);
        return querySuppleStatusFacade.execute(querySuppleStatusRequest);
    }


    /**
     * 设置输出的kyc信息
     *
     * @param kycInfoResponse
     * @return KycInfoDTO
     */
    private KycInfoDTO setOutKycInfo(KycInfoResponse kycInfoResponse) {
        KycInfoDTO kycInfoDTO = new KycInfoDTO();
        if (kycInfoResponse == null) {
            return kycInfoDTO;
        }

        kycInfoDTO.setInvestorType(kycInfoResponse.getInvestorType());
        kycInfoDTO.setRiskToleranceLevel(kycInfoResponse.getRiskToleranceLevel());
        return kycInfoDTO;
    }

    // 构造返回信息
    private BuyConfirmDto buildBuyComfirmDto(PiggyFundTxOpenCfgModel piggyCfg, HighProductInfoModel product, DealOrderBean order,
                                             List<SavingBoxVolDetail> piggyBankCardList, List<HighProductFeeRateModel> fundFeeRateList, List<CustBankModel> custBindBankList,
                                             HighProductActiDiscountListModel model, PrebookDetailDto prebookDetailDto) {
        BuyConfirmDto dto = new BuyConfirmDto();
        // 是否支持储蓄罐支付
        dto.setShowSavingBox(showSavingBox(piggyCfg, product.getPaymentTypeList()));
        // 是否支持自划款0-不支持1-支持
        dto.setIsSupSelfDrawing(BusiUtil.getSuportPayType(product.getPaymentTypeList(), BusiUtil.SUPORT_SELF_BANK_POSI));
        // 是否支持代扣0-不支持 1-支持
        dto.setIsSupAgentDrawing(BusiUtil.getSuportPayType(product.getPaymentTypeList(), BusiUtil.SUPORT_BANK_POSI));
        // 收费类型 A-前收费 B-后收费
        dto.setShareClass(product.getShareClass());
        // 产品通道 3-群济私募 5-好买公募
        dto.setProductChannel(product.getProductChannel());
        // 最近一次支付方式
        dto.setLastTimePayChannelCode(order == null ? "" : order.getPaymentType());
        // 最近一次支付银行卡
        dto.setLastTimeAccoundId(order == null ? "" : order.getBankAcct());
        // 储蓄罐限额
        dto.setWithdrawLimitDto(piggyCfg);
        // 储蓄罐份额列表
        dto.setVolDtlDtoList(piggyBankCardList);
        // 基础费率列表
        dto.setFundFeeRateList(fundFeeRateList);
        // 用户绑定银行卡列表
        dto.setBankAccountList(custBindBankList);
        // 代销折扣率
        dto.setAgendDisc(model.getAgentDisc());
        // 活动折扣率
        dto.setHighProductActiDiscountModelList(model.getDiscountList());
        // 获取用户绑定的所有银行代码，兼容前端判断是否支持快捷鉴权。这次不需要判断是否支持快捷鉴权
        dto.setSupportQuickAuthBankCode(getSupportQuickAuthBankCode(custBindBankList));
        // 预约信息
        dto.setSubsciptionVO(prebookDetailDto);
        // 手续费计算类型 0-外扣法；1-内扣法
        dto.setFeeCalMode(product.getFeeCalMode());
        // 活动折扣
        dto.setActivityRate(model.getDiscountList().get(0).getDiscountRatio());
        // 是否支持储蓄个预约支付
        dto.setSuportPiggyAppointPay(BusiUtil.getSuportPayType(product.getPaymentTypeList(), BusiUtil.SUPORT_PIGGY_APPOINT_POSI));
        return dto;
    }


    /**
     * showSavingBox:(是否支持储蓄)
     *
     * @param piggyCfg        储蓄罐取现限制
     * @param paymentTypeList 产品支持购买方式列表
     * @return
     * <AUTHOR>
     * @date 2018年2月26日 下午1:13:53
     */
    private boolean showSavingBox(PiggyFundTxOpenCfgModel piggyCfg, String paymentTypeList) {
        boolean isSubbortSavingBox = "1".equals(BusiUtil.getSuportPayType(paymentTypeList, BusiUtil.SUPORT_PIGGY_POSI));

        if (null != piggyCfg && isSubbortSavingBox) {
            // RedeFlag 是否开通储蓄罐普通赎回0-未开通；1-已开通
            // newProductState 是否开通储蓄罐支付 0-未开通 1-开通
            return "1".equals(piggyCfg.getRedeFlag()) && "1".equals(piggyCfg.getNewProductState());
        }
        return false;
    }

    /**
     * isSupportSavingBox:中台产品配置是否支持储蓄罐
     *
     * @param paymentTypeList
     * @return
     * <AUTHOR>
     * @date 2018年11月7日 下午2:01:09
     */
    private boolean isSupportSavingBox(String paymentTypeList) {
        return "1".equals(BusiUtil.getSuportPayType(paymentTypeList, BusiUtil.SUPORT_PIGGY_POSI));
    }

    /**
     * getPiggyOpenCfg:后台储蓄罐是否开通
     *
     * @param paymentTypeList
     * @return
     * <AUTHOR>
     * @date 2018年11月7日 下午2:10:49
     */
    private PiggyFundTxOpenCfgModel getPiggyOpenCfg(String paymentTypeList) {
        PiggyFundTxOpenCfgModel model = null;
        if (isSupportSavingBox(paymentTypeList)) {
            model = piggyTradeService.queryPiggyFundTxOpenCfg();
        }
        return model;
    }

    /**
     * sortCustBankInfo:银行卡列表重排序：已签约-未签约-不支持快捷
     *
     * @param bankCardInfos
     * @param supportQuickAuthBankCode
     * @return
     * <AUTHOR>
     * @date 2018年10月30日 下午3:39:49
     */
    private List<CustBankModel> sortCustBankInfo(List<CustBankModel> bankCardInfos, Set<String> supportQuickAuthBankCode) {
        // 已代扣签约,支持快捷鉴权银行卡
        List<CustBankModel> supportQuickAuthBankList = new ArrayList<CustBankModel>();
        // 未代扣签约,支持快捷鉴权银行卡
        List<CustBankModel> notSignBankList = new ArrayList<CustBankModel>();
        // 不支持快捷鉴权银行卡
        List<CustBankModel> notSupportQuickAuthBankList = new ArrayList<CustBankModel>();
        for (CustBankModel custBankModel : bankCardInfos) {
            if (!supportQuickAuthBankCode.contains(custBankModel.getBankCode())) {
                // 不支持快捷鉴权的银卡卡
                notSupportQuickAuthBankList.add(custBankModel);
            } else {
                if (PaySignEnum.OPEN.getCode().equals(custBankModel.getPaySign())) {
                    // 开通代扣签约
                    supportQuickAuthBankList.add(custBankModel);
                } else {
                    // 未开通代扣签约
                    notSignBankList.add(custBankModel);
                }
            }
        }
        // 绑定的已开通代扣签约，支持快捷鉴权 银行卡根据签约日期排序和额度排序
        BusiUtil.bindBankCardSortBySignDt(supportQuickAuthBankList);

        List<CustBankModel> custBindBankList = new ArrayList<CustBankModel>();
        // 已签约，支持快捷鉴权的银行卡
        custBindBankList.addAll(supportQuickAuthBankList);
        // 未签约，支持快捷鉴权的银行卡
        custBindBankList.addAll(notSignBankList);
        // 不支持快捷鉴权的银行卡
        custBindBankList.addAll(notSupportQuickAuthBankList);
        return custBindBankList;
    }

    /**
     * getSupportQuickAuthBankCode:获取支持快捷鉴权银行卡
     *
     * @param bankCardInfos
     * @return
     * <AUTHOR>
     * @date 2018年10月30日 下午2:47:19
     */
    private Set<String> getSupportQuickAuthBankCode(List<CustBankModel> bankCardInfos) {
        Set<String> supportQuickAuthBankCode = new HashSet<String>();
        if (!CollectionUtils.isEmpty(bankCardInfos)) {
            for (CustBankModel custBankModel : bankCardInfos) {
                if (supportQuickAuth(custBankModel.getBankCode())) {
                    supportQuickAuthBankCode.add(custBankModel.getBankCode());
                }
            }
        }
        return supportQuickAuthBankCode;
    }

    /**
     * @param bankCode
     * @return boolean
     * @Description 支持快捷鉴权的银行
     * <AUTHOR>
     * @Date 2019/9/25 9:21
     **/
    private boolean supportQuickAuth(String bankCode) {
        QueryAuthTypeRequest queryAuthTypeRequest = new QueryAuthTypeRequest();
        queryAuthTypeRequest.setProdLqdType(ProdLqdTypeEnum.HOWBUY_FUND);
        queryAuthTypeRequest.setBankCode(bankCode);
        QueryAuthTypeResponse queryAuthTypeResponse = queryAuthTypeFacade.execute(queryAuthTypeRequest);
        if (null != queryAuthTypeResponse && "0000000".equals(queryAuthTypeResponse.getReturnCode()) && null != queryAuthTypeResponse.getAuthTypeVos()
                && !queryAuthTypeResponse.getAuthTypeVos().isEmpty()) {
            for (AuthTypeVo authTypeVo : queryAuthTypeResponse.getAuthTypeVos()) {
                if (AuthTypeEnum.QUICK_AUTH.equals(authTypeVo.getAuthType())) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * getPiggyBankCardList:查询储蓄罐卡信息
     *
     * @param highProductBaseModel
     * @param txAcctNo
     * @param dealOrderBean
     * @return
     * <AUTHOR>
     * @date 2018年10月30日 下午2:41:39
     */
    private List<SavingBoxVolDetail> getPiggyBankCardList(HighProductInfoModel highProductBaseModel, String txAcctNo, DealOrderBean dealOrderBean) {
        List<SavingBoxVolDetail> piggyBankCardList = new ArrayList<>();
        if (!isSupportSavingBox(highProductBaseModel.getPaymentTypeList())) {
            return piggyBankCardList;
        }
        // 查询储蓄罐的限额
        piggyBankCardList = piggyTradeService.queryVolDetailByCustBankId(txAcctNo, "1");
        List<SavingBoxVolDetail> volDtlDtoListBak = new ArrayList<SavingBoxVolDetail>();
        for (SavingBoxVolDetail savingBoxVolDetail : piggyBankCardList) {
            if (null != savingBoxVolDetail.getAvailAmt() && savingBoxVolDetail.getAvailRegAmt().doubleValue() > 0) {
                volDtlDtoListBak.add(savingBoxVolDetail);
            }
        }
        piggyBankCardList = volDtlDtoListBak;
        // 基金有单卡操作 3-TA 单卡， 2-产品单卡
        if (SupportCardTypeEnum.PRODUCT_SINGLE_CARD.getCode().equals(highProductBaseModel.getSupportCardType())
                || SupportCardTypeEnum.TA_SINGLE_CARD.getCode().equals(highProductBaseModel.getSupportCardType())) {
            if (dealOrderBean != null) {
                List<SavingBoxVolDetail> piggyBankCardLists = new ArrayList<SavingBoxVolDetail>();
                for (SavingBoxVolDetail savingBoxVolDetail : piggyBankCardList) {
                    if (dealOrderBean.getCpAcctNo().equals(savingBoxVolDetail.getCustBankId())) {
                        piggyBankCardLists.add(savingBoxVolDetail);
                        break;
                    }
                }
                return piggyBankCardLists;
            }
        }
        // 可用储蓄罐份额，根据可用注册份额排序
        BusiUtil.piggySortByVol(piggyBankCardList);
        return piggyBankCardList;
    }

    /**
     * getBankCardInfos:查询银行卡信息
     *
     * @param highProductBaseModel
     * @param txAcctNo
     * @param dealOrderBean
     * @return
     * <AUTHOR>
     * @date 2018年10月30日 下午2:41:10
     */
    private List<CustBankModel> getBankCardInfos(HighProductInfoModel highProductBaseModel, String txAcctNo, DealOrderBean dealOrderBean) {
        List<CustBankModel> bankCardInfos = new ArrayList<>();
        // 走公募的渠道
        if (ProductChannelEnum.HIGH_FUND.getCode().equals(highProductBaseModel.getProductChannel())) {
            bankCardInfos = bankCardService.queryBindBankCards(txAcctNo);
        }
        // 走私募的渠道
        if (ProductChannelEnum.TP_SM.getCode().equals(highProductBaseModel.getProductChannel())) {
            bankCardInfos = bankCardService.queryBindBankCards(null, txAcctNo, "102");
        }

        // 基金有单卡操作2-产品单卡 3-TA单卡
        if (SupportCardTypeEnum.PRODUCT_SINGLE_CARD.getCode().equals(highProductBaseModel.getSupportCardType())
                || SupportCardTypeEnum.TA_SINGLE_CARD.getCode().equals(highProductBaseModel.getSupportCardType())) {
            if (dealOrderBean != null) {
                List<CustBankModel> custBankList = new ArrayList<CustBankModel>();
                for (CustBankModel custBankModel : bankCardInfos) {
                    if (dealOrderBean.getCpAcctNo().equals(custBankModel.getCustBankId())) {
                        custBankList.add(custBankModel);
                        break;
                    }
                }
                return custBankList;
            }
        }
        return bankCardInfos;
    }

    /**
     * getLastBuyDeal:(获取追近一次购买记录)
     *
     * @param txAcctNo
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2018年2月26日 下午1:26:26
     */
    private DealOrderBean getLastBuyDeal(String txAcctNo, String fundCode) {
        log.info("BuyController|getLastBuyDeal|txAcctNo:{}, fundCode:{}", txAcctNo, fundCode);
        QueryDealOrderListRequest queryDealOrderListRequest = new QueryDealOrderListRequest();
        queryDealOrderListRequest.setTxAcctNo(txAcctNo);
        queryDealOrderListRequest.setProductCode(fundCode);
        queryDealOrderListRequest.setmBusiCodeArr(new String[]{"1120", "1122"});
        queryDealOrderListRequest.setOrderStatusArr(new String[]{"1", "2", "3"});
        QueryDealOrderListResponse queryDealOrderListResponse = queryDealOrderListFacade.execute(queryDealOrderListRequest);
        if (null != queryDealOrderListResponse && !CollectionUtils.isEmpty(queryDealOrderListResponse.getDealOrderList())) {
            return queryDealOrderListResponse.getDealOrderList().get(0);

        }

        return null;
    }

    // 获取手机号
    private QueryBankCardInfoResponse getBankCardInfo(String txAcctNo, String custBankId, String disCode) {
        QueryBankCardInfoRequest queryBankCardInfoRequest = new QueryBankCardInfoRequest();
        queryBankCardInfoRequest.setTxAcctNo(txAcctNo);
        queryBankCardInfoRequest.setCpAcctNo(custBankId);
        queryBankCardInfoRequest.setCpAcctSwitch(true);
        queryBankCardInfoRequest.setDisCode(disCode);
        return queryBankCardInfoFacade.execute(queryBankCardInfoRequest);
    }

    // 获取手机号
    private String getBankMobile(String hbOneNo, String cpAcctNo, String disCode) {
        Map<String, String> map = accPlaintextService.queryCustBankMobileMap(null, hbOneNo, disCode);
        if (map != null) {
            return map.get(cpAcctNo);
        }
        return null;
    }

    /**
     * 获取一账通绑定的手机号
     *
     * @param hboneNo
     * @return String
     */
    private String getHboneBindMobileMask(String hboneNo) {
        QueryHboneInfoRequest request = new QueryHboneInfoRequest();
        request.setHboneNo(hboneNo);
        BaseHboneInfoResponse response = querySingleService.query(request);
        if (response == null || !response.isSuccessful()) {
            return null;
        }

        return response.getMobileMask();
    }

}
