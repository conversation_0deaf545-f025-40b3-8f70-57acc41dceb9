package com.howbuy.cgi.trade.simu.model.dto;

import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:高端持有基金
 * @reason:
 * <AUTHOR>
 * @date 2018年6月21日 下午9:07:14
 * @since JDK 1.7
 */
public class IndexDTO implements Serializable {
	private static final long serialVersionUID = 3108964422094630532L;
	/**
	 * 总资产
	 */
	private double totalAsset;
	/**
	 * 总待确认金额
	 */
	private double totalUnconfirmedAmt;
	/**
	 * 是否需要资产证明
	 */
	private String isVerifyAssetCertify;
	/**
	 * 资产证明状态 :0-审核中 ; 1-通过 ;2-未通过 ;3-已过期
	 */
	private String verifyStatus;
	/**
	 * 是否专业投资者:1- 是; 0- 否
	 */
	private String isProfessor = "0";
	/**
	 * 待确认交易笔数
	 */
	private int totalUnconfirmedNum;
	/**
	 * 当前总收益
	 */
	private BigDecimal totalCurrentAsset;
	/**
	 * 总回款
	 */
	private BigDecimal totalCashCollection;
	/**
	 * 总收益计算状态:0-计算中;1-计算成功
	 */
	private String  totalIncomCalStat;
	/**
	 * 赎回在途笔数
	 */
	private int redeemUnconfirmedNum;
	/**
	 * 公募未开户 1 是 0 否
	 */
	private String noTxAcctNo = "0";
	/**
	 * 是否不绑定定登录，用于特定用户不支持微信绑定自登陆
	 */
	private String isnologinBind;
	/**
	 * 展示关联账户入口 1 是 0 否
	 */
	private String showRelatedAccount;
	/**
	 * 关联客户姓名
	 */
	private String relatedCustName;
	/**
	 * 关联客户一账通
	 */
	private String relatedHboneNo;
	/**
	 * 服务器日期
	 */
	private String serverData;
	/**
	 * 是否含有私募定投 0-不含 1-含
	 */
	private String highFundInvPlanFlag;
	/**
	 * 是否持有好臻产品 0:没有,1:有
	 */
	private String hasHZProduct;
	/**
	 * 是否持有好买香港产品  0:没有,1:有
	 */
	private String hasHKProduct;
	/**
	 * 是否授权  0:没有,1:有授权
	 */
	private String isAuth;


	/**
	 * 是否香港数据隔离,1:是,0:否
	 */
	private String isHkDataQuarantine;
	/**
	 * 待付款订单
	 */
	private List<QueryAcctBalanceResponse.DealOrderBean> unpaidList;
	/**
	 * 待确认订单
	 */
	private List<QueryAcctBalanceResponse.DealOrderBean> unconfirmedList;

	/**
	 * 购买待退款订单数
	 */
	private Integer buyUnrefundedPiece;
	/**
	 * 赎回待回款订单数
	 */
	private Integer redeemUnrefundedPiece;

	public String getHasHZProduct() {
		return hasHZProduct;
	}

	public void setHasHZProduct(String hasHZProduct) {
		this.hasHZProduct = hasHZProduct;
	}

	public String getHasHKProduct() {
		return hasHKProduct;
	}

	public void setHasHKProduct(String hasHKProduct) {
		this.hasHKProduct = hasHKProduct;
	}

	public String getIsAuth() {
		return isAuth;
	}

	public void setIsAuth(String isAuth) {
		this.isAuth = isAuth;
	}

	public String getIsHkDataQuarantine() {
		return isHkDataQuarantine;
	}

	public void setIsHkDataQuarantine(String isHkDataQuarantine) {
		this.isHkDataQuarantine = isHkDataQuarantine;
	}

	public List<QueryAcctBalanceResponse.DealOrderBean> getUnpaidList() {
		return unpaidList;
	}

	public void setUnpaidList(List<QueryAcctBalanceResponse.DealOrderBean> unpaidList) {
		this.unpaidList = unpaidList;
	}

	public List<QueryAcctBalanceResponse.DealOrderBean> getUnconfirmedList() {
		return unconfirmedList;
	}

	public void setUnconfirmedList(List<QueryAcctBalanceResponse.DealOrderBean> unconfirmedList) {
		this.unconfirmedList = unconfirmedList;
	}

	public Integer getBuyUnrefundedPiece() {
		return buyUnrefundedPiece;
	}

	public void setBuyUnrefundedPiece(Integer buyUnrefundedPiece) {
		this.buyUnrefundedPiece = buyUnrefundedPiece;
	}

	public Integer getRedeemUnrefundedPiece() {
		return redeemUnrefundedPiece;
	}

	public void setRedeemUnrefundedPiece(Integer redeemUnrefundedPiece) {
		this.redeemUnrefundedPiece = redeemUnrefundedPiece;
	}

	public BigDecimal getTotalCurrentAsset() {
        return totalCurrentAsset;
    }

    public void setTotalCurrentAsset(BigDecimal totalCurrentAsset) {
        this.totalCurrentAsset = totalCurrentAsset;
    }
    
    public String getTotalIncomCalStat() {
        return totalIncomCalStat;
    }

    public void setTotalIncomCalStat(String totalIncomCalStat) {
        this.totalIncomCalStat = totalIncomCalStat;
    }

    private List<HoldFund> funds = new ArrayList<HoldFund>();

	private List<UnconfirmeFund> unconfirmedFunds = new ArrayList<UnconfirmeFund>();

	public List<UnconfirmeFund> getUnconfirmedFunds() {
		return unconfirmedFunds;
	}

	public void setUnconfirmedFunds(List<UnconfirmeFund> unconfirmedFunds) {
		this.unconfirmedFunds = unconfirmedFunds;
	}

	public void addFund(HoldFund fund) {
		funds.add(fund);
	}

	public double getTotalAsset() {
		return totalAsset;
	}

	public void setTotalAsset(double totalAsset) {
		this.totalAsset = totalAsset;
	}

	public List<HoldFund> getFunds() {
		return funds;
	}

	public void setFunds(List<HoldFund> funds) {
		this.funds = funds;
	}

	public String getIsVerifyAssetCertify() {
		return isVerifyAssetCertify;
	}

	public void setIsVerifyAssetCertify(String isVerifyAssetCertify) {
		this.isVerifyAssetCertify = isVerifyAssetCertify;
	}

	public String getVerifyStatus() {
		return verifyStatus;
	}

	public void setVerifyStatus(String verifyStatus) {
		this.verifyStatus = verifyStatus;
	}

	public String getIsProfessor() {
		return isProfessor;
	}

	public void setIsProfessor(String isProfessor) {
		this.isProfessor = isProfessor;
	}

	public double getTotalUnconfirmedAmt() {
		return totalUnconfirmedAmt;
	}

	public void setTotalUnconfirmedAmt(double totalUnconfirmedAmt) {
		this.totalUnconfirmedAmt = totalUnconfirmedAmt;
	}

	public int getTotalUnconfirmedNum() {
		return totalUnconfirmedNum;
	}

	public void setTotalUnconfirmedNum(int totalUnconfirmedNum) {
		this.totalUnconfirmedNum = totalUnconfirmedNum;
	}

	public int getRedeemUnconfirmedNum() {
		return redeemUnconfirmedNum;
	}

	public void setRedeemUnconfirmedNum(int redeemUnconfirmedNum) {
		this.redeemUnconfirmedNum = redeemUnconfirmedNum;
	}

	public String getNoTxAcctNo() {
		return noTxAcctNo;
	}

	public void setNoTxAcctNo(String noTxAcctNo) {
		this.noTxAcctNo = noTxAcctNo;
	}

	public String getIsnologinBind() {
		return isnologinBind;
	}

	public void setIsnologinBind(String isnologinBind) {
		this.isnologinBind = isnologinBind;
	}

	public String getShowRelatedAccount() {
		return showRelatedAccount;
	}

	public void setShowRelatedAccount(String showRelatedAccount) {
		this.showRelatedAccount = showRelatedAccount;
	}

	public String getRelatedCustName() {
		return relatedCustName;
	}

	public void setRelatedCustName(String relatedCustName) {
		this.relatedCustName = relatedCustName;
	}

	public String getRelatedHboneNo() {
		return relatedHboneNo;
	}

	public void setRelatedHboneNo(String relatedHboneNo) {
		this.relatedHboneNo = relatedHboneNo;
	}

	public BigDecimal getTotalCashCollection() {
		return totalCashCollection;
	}

	public void setTotalCashCollection(BigDecimal totalCashCollection) {
		this.totalCashCollection = totalCashCollection;
	}

	public String getServerData() {
		return serverData;
	}

	public void setServerData(String serverData) {
		this.serverData = serverData;
	}

	public String getHighFundInvPlanFlag() {
		return highFundInvPlanFlag;
	}

	public void setHighFundInvPlanFlag(String highFundInvPlanFlag) {
		this.highFundInvPlanFlag = highFundInvPlanFlag;
	}
	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
