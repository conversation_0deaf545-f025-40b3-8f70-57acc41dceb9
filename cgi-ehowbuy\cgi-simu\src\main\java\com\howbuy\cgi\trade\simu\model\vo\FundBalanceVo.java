package com.howbuy.cgi.trade.simu.model.vo;

import com.howbuy.cgi.trade.simu.common.enums.CurrencyEnum;
import com.howbuy.cgi.trade.simu.model.dto.BaseDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:产品持仓信息
 * @Author: yun.lu
 * Date: 2023/9/27 15:05
 */
public class FundBalanceVo extends BaseDto {
    /**
     * 产品代码
     */
    private String fundCode;

    /**
     * 产品名称
     */
    private String fundName;

    /**
     * 是否分期成立,1-是;0-否
     */
    private String stageEstablishFlag;

    /**
     * 当前收益（当前币种）
     */
    private BigDecimal currentAssetCurrency;

    /**
     * 当前收益（人民币）
     */
    private BigDecimal currentAsset;

    /**
     * 总市值(人民币)
     */
    private BigDecimal totalAsset;


    /**
     * 持仓笔数
     */
    private int balanceNum;

    /**
     * 总份额
     */
    private BigDecimal totalBalanceVol;

    /**
     * 私募股权回款金额
     */
    private BigDecimal cashCollection;

    /**
     * 私募股权回款金额String
     */
    private String cashCollectionStr;
    /**
     * 总市值(当前币种)
     */
    private BigDecimal currencyMarketValue;

    /**
     * 是否显示收益率 1 是 0 否
     */
    private String isShowYieldRate;

    /**
     * 产品集合类型
     *
     * @see com.howbuy.cgi.trade.simu.common.enums.FundSetTypeEnum
     */
    private String fundSetType;

    /**
     * 净值分红标识,0-否，1-是
     */
    private String navDivFlag;

    /**
     * 产品类型,7-一对多专户,11-私募
     */
    private String productType;

    /**
     * 产品二级类型:0-现金管理,1-债券,2-类固定收益,3-对冲策略,4-股票策略,5-股权,6-房地产基金,7-其他
     */
    private String productSubType;

    /**
     * 异常标识	0-否; 1-是
     */
    private String abnormalState;

    /**
     * 清算标识    0-否; 1-是
     */
    private String crisisFlag;

    /**
     * 千禧年待投产品标识	0-否; 1-是
     */
    private String qianXiFlag;

    /**
     * 总市值(人民币)
     */
    private BigDecimal marketValue;

    /**
     * 当前收益率
     */
    private BigDecimal yieldRate;

    /**
     * 净值
     */
    private String nav;

    /**
     * 净值更新时间,YYYYMMDD或者MMDD
     */
    private String navDate;

    /**
     * 净值披露方式,1-净值,2-份额收益
     */
    private String navDisclosureType;

    /**
     * 收益计算状态 0-计算中;1-计算成功
     */
    private String incomeCalStat;

    /**
     * 币种
     *
     * @see CurrencyEnum
     */
    private String currencyCode;

    /**
     * 币种描述
     *
     * @see CurrencyEnum
     */
    private String currencyDesc;

    /**
     * 标准固收标识（固收类产品有此标识）,0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品
     */
    private String standardFixedIncomeFlag;

    /**
     * 到期日
     */
    private String dueDate;

    /**
     * 固收货币产品七日年化收益（来自DB）
     */
    private BigDecimal yieldIncome;

    /**
     * 产品存续期限(类似于5+3+2这种说明)
     */
    private String productDuration;

    /**
     * 私募股权回款(当前币种)
     */
    private BigDecimal currencyCashCollection;

    /**
     * 产品期限说明
     */
    private String cpqxsm;

    /**
     * 待投金额（人民币）
     */
    private BigDecimal unPaidInAmt;

    /**
     * 总实缴金额
     */
    private BigDecimal paidInAmt;

    /**
     * 待投金额（当前币种）
     */
    private BigDecimal currencyUnPaidInAmt;

    /**
     * 净购买金额(投资成本)(当前币种)
     */
    private BigDecimal currencyNetBuyAmount;
    /**
     * 产品渠道
     */
    private String disCode;

    /**
     * 好买香港代销标识: 0-否; 1-是
     */
    private String hkSaleFlag;

    /**
     * 固收货币产品净值日期（来自DB）
     */
    private String yieldIncomeDt;

    /**
     * 为了保持跟老接口返回产品顺序一致,加的产品原排序
     */
    private int sort;

    /**
     * 产品详细信息(如果是分期成立,则会有多条)
     */
    private List<FundItemVo> fundItemVoList;

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getStageEstablishFlag() {
        return stageEstablishFlag;
    }

    public void setStageEstablishFlag(String stageEstablishFlag) {
        this.stageEstablishFlag = stageEstablishFlag;
    }

    public BigDecimal getCurrentAssetCurrency() {
        return currentAssetCurrency;
    }

    public void setCurrentAssetCurrency(BigDecimal currentAssetCurrency) {
        this.currentAssetCurrency = currentAssetCurrency;
    }

    public BigDecimal getCurrentAsset() {
        return currentAsset;
    }

    public void setCurrentAsset(BigDecimal currentAsset) {
        this.currentAsset = currentAsset;
    }

    public BigDecimal getTotalAsset() {
        return totalAsset;
    }

    public void setTotalAsset(BigDecimal totalAsset) {
        this.totalAsset = totalAsset;
    }

    public int getBalanceNum() {
        return balanceNum;
    }

    public void setBalanceNum(int balanceNum) {
        this.balanceNum = balanceNum;
    }

    public BigDecimal getTotalBalanceVol() {
        return totalBalanceVol;
    }

    public void setTotalBalanceVol(BigDecimal totalBalanceVol) {
        this.totalBalanceVol = totalBalanceVol;
    }

    public BigDecimal getCashCollection() {
        return cashCollection;
    }

    public void setCashCollection(BigDecimal cashCollection) {
        this.cashCollection = cashCollection;
    }

    public String getCashCollectionStr() {
        return cashCollectionStr;
    }

    public void setCashCollectionStr(String cashCollectionStr) {
        this.cashCollectionStr = cashCollectionStr;
    }

    public BigDecimal getCurrencyMarketValue() {
        return currencyMarketValue;
    }

    public void setCurrencyMarketValue(BigDecimal currencyMarketValue) {
        this.currencyMarketValue = currencyMarketValue;
    }

    public String getIsShowYieldRate() {
        return isShowYieldRate;
    }

    public void setIsShowYieldRate(String isShowYieldRate) {
        this.isShowYieldRate = isShowYieldRate;
    }

    public String getFundSetType() {
        return fundSetType;
    }

    public void setFundSetType(String fundSetType) {
        this.fundSetType = fundSetType;
    }

    public String getNavDivFlag() {
        return navDivFlag;
    }

    public void setNavDivFlag(String navDivFlag) {
        this.navDivFlag = navDivFlag;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getProductSubType() {
        return productSubType;
    }

    public void setProductSubType(String productSubType) {
        this.productSubType = productSubType;
    }

    public String getAbnormalState() {
        return abnormalState;
    }

    public void setAbnormalState(String abnormalState) {
        this.abnormalState = abnormalState;
    }

    public String getCrisisFlag() {
        return crisisFlag;
    }

    public void setCrisisFlag(String crisisFlag) {
        this.crisisFlag = crisisFlag;
    }

    public String getQianXiFlag() {
        return qianXiFlag;
    }

    public void setQianXiFlag(String qianXiFlag) {
        this.qianXiFlag = qianXiFlag;
    }

    public BigDecimal getMarketValue() {
        return marketValue;
    }

    public void setMarketValue(BigDecimal marketValue) {
        this.marketValue = marketValue;
    }

    public BigDecimal getYieldRate() {
        return yieldRate;
    }

    public void setYieldRate(BigDecimal yieldRate) {
        this.yieldRate = yieldRate;
    }

    public String getNav() {
        return nav;
    }

    public void setNav(String nav) {
        this.nav = nav;
    }

    public String getNavDate() {
        return navDate;
    }

    public void setNavDate(String navDate) {
        this.navDate = navDate;
    }

    public String getNavDisclosureType() {
        return navDisclosureType;
    }

    public void setNavDisclosureType(String navDisclosureType) {
        this.navDisclosureType = navDisclosureType;
    }

    public String getIncomeCalStat() {
        return incomeCalStat;
    }

    public void setIncomeCalStat(String incomeCalStat) {
        this.incomeCalStat = incomeCalStat;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getCurrencyDesc() {
        return currencyDesc;
    }

    public void setCurrencyDesc(String currencyDesc) {
        this.currencyDesc = currencyDesc;
    }

    public String getStandardFixedIncomeFlag() {
        return standardFixedIncomeFlag;
    }

    public void setStandardFixedIncomeFlag(String standardFixedIncomeFlag) {
        this.standardFixedIncomeFlag = standardFixedIncomeFlag;
    }

    public String getDueDate() {
        return dueDate;
    }

    public void setDueDate(String dueDate) {
        this.dueDate = dueDate;
    }

    public BigDecimal getYieldIncome() {
        return yieldIncome;
    }

    public void setYieldIncome(BigDecimal yieldIncome) {
        this.yieldIncome = yieldIncome;
    }

    public String getProductDuration() {
        return productDuration;
    }

    public void setProductDuration(String productDuration) {
        this.productDuration = productDuration;
    }

    public BigDecimal getCurrencyCashCollection() {
        return currencyCashCollection;
    }

    public void setCurrencyCashCollection(BigDecimal currencyCashCollection) {
        this.currencyCashCollection = currencyCashCollection;
    }

    public String getCpqxsm() {
        return cpqxsm;
    }

    public void setCpqxsm(String cpqxsm) {
        this.cpqxsm = cpqxsm;
    }

    public BigDecimal getUnPaidInAmt() {
        return unPaidInAmt;
    }

    public void setUnPaidInAmt(BigDecimal unPaidInAmt) {
        this.unPaidInAmt = unPaidInAmt;
    }

    public BigDecimal getPaidInAmt() {
        return paidInAmt;
    }

    public void setPaidInAmt(BigDecimal paidInAmt) {
        this.paidInAmt = paidInAmt;
    }

    public BigDecimal getCurrencyUnPaidInAmt() {
        return currencyUnPaidInAmt;
    }

    public void setCurrencyUnPaidInAmt(BigDecimal currencyUnPaidInAmt) {
        this.currencyUnPaidInAmt = currencyUnPaidInAmt;
    }

    public BigDecimal getCurrencyNetBuyAmount() {
        return currencyNetBuyAmount;
    }

    public void setCurrencyNetBuyAmount(BigDecimal currencyNetBuyAmount) {
        this.currencyNetBuyAmount = currencyNetBuyAmount;
    }

    public String getDisCode() {
        return disCode;
    }

    public void setDisCode(String disCode) {
        this.disCode = disCode;
    }

    public String getHkSaleFlag() {
        return hkSaleFlag;
    }

    public void setHkSaleFlag(String hkSaleFlag) {
        this.hkSaleFlag = hkSaleFlag;
    }

    public String getYieldIncomeDt() {
        return yieldIncomeDt;
    }

    public void setYieldIncomeDt(String yieldIncomeDt) {
        this.yieldIncomeDt = yieldIncomeDt;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public List<FundItemVo> getFundItemVoList() {
        return fundItemVoList;
    }

    public void setFundItemVoList(List<FundItemVo> fundItemVoList) {
        this.fundItemVoList = fundItemVoList;
    }
}
