/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.cgi.trade.simu.service.relatedaccount;

import com.alibaba.fastjson.JSON;
import com.howbuy.cc.message.send.auto.request.SendMsgByHboneNoRequest;
import com.howbuy.cc.message.send.auto.response.SendMsgByHboneNoResponse;
import com.howbuy.cc.message.send.auto.service.AutoSendMessageService;
import com.howbuy.cgi.common.util.RemoteUtil;
import com.howbuy.cgi.trade.simu.service.QueryBalanceVolService;
import com.howbuy.cms.form.RequestShortLinkForm;
import com.howbuy.cms.service.base.ShortLinkService;
import com.howbuy.crm.nt.pushmsg.request.CmPushMsgRequest;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import com.howbuy.tms.common.utils.DateUtils;
import crm.howbuy.base.dubbo.response.BaseResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 关联账户消息服务
 *
 * <AUTHOR>
 * @date 2021/7/19 13:51
 * @since JDK 1.8
 */
@Service("messageService")
public class MessageService {

    private static final Logger logger = LogManager.getLogger(MessageService.class);

    @Autowired
    @Qualifier("simu.cmPushMsgService")
    private CmPushMsgService cmPushMsgService;
    @Autowired
    @Qualifier("simu.shortLinkService")
    private ShortLinkService shortLinkService;
    @Autowired
    @Qualifier("simu.autoSendMessageService")
    private AutoSendMessageService autoSendMessageService;

    /**
     * 给投顾发消息
     *
     * @param businessId
     * @param hboneNo
     * @param params
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/7/19 16:13
     * @since JDK 1.8
     */
    public void sendMsgToConsultant(String businessId, String hboneNo, Map<String, Object> params) {
        sendMsgToConsultant(businessId, hboneNo, JSON.toJSONString(params));
    }

    /**
     * 给投顾发消息
     *
     * @param businessId
     * @param hboneNo
     * @param paramJson
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/7/19 16:13
     * @since JDK 1.8
     */
    public void sendMsgToConsultant(String businessId, String hboneNo, String paramJson) {
        try {
            CmPushMsgRequest request = new CmPushMsgRequest();
            request.setBusinessId(businessId);
            request.setAccount(hboneNo);
            request.setAccountType("1");
            request.setParamJson(paramJson);
            BaseResponse response = cmPushMsgService.pushMsg(request);
            if (!RemoteUtil.isSuccess(response.getReturnCode())) {
                logger.error("pushMsgToConsultant fail. res:{}", JSON.toJSONString(response));
            }
        } catch (Throwable e) {
            logger.error("pushMsgToConsultant fail. error:{}", e.getMessage(), e);
        }
    }

    /**
     * 获取短连接
     *
     * @param url
     * @param endTime
     * @return java.lang.String
     * @author: huaqiang.liu
     * @date: 2021/7/16 17:49
     * @since JDK 1.8
     */
    public String getShortLink(String url, Date endTime) {
        RequestShortLinkForm request = new RequestShortLinkForm();
        request.setLink(url);
        request.setCreate_user("relatedAccount");
        request.setEnd_time(DateUtils.formatToString(endTime, DateUtils.YYYY_MM_DD_HH_MM_SS));
        return shortLinkService.addShortLink(request);
    }

    /**
     * 发送消息
     *
     * @param businessId
     * @param hboneNo
     * @param params
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/7/19 20:19
     * @since JDK 1.8
     */
    public void sendByHboneNo(String businessId, String hboneNo, Map<String, Object> params) {
        try {
            Map<String, Object> var = new HashMap<>(1);
            var.put("params", params);
            SendMsgByHboneNoRequest request = new SendMsgByHboneNoRequest();
            request.setBusinessId(businessId);
            request.setHboneNo(hboneNo);
            request.setTemplateVar(JSON.toJSONString(var));
            SendMsgByHboneNoResponse response = autoSendMessageService.sendMsgByHboneNo(request);
            logger.info("sendByHboneNo-发送消息结果,request={},response={}", JSON.toJSONString(request), JSON.toJSONString(response));
        } catch (Throwable e) {
            logger.error("sendByHboneNo fail. businessId:{} hboneNo:{} params:{} error:{}", businessId, hboneNo, params, e.getMessage(), e);
        }
    }

}