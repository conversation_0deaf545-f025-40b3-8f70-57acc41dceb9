# 短信模板配置说明

## 业务背景
根据关联账户相关需求，需要对家庭账户邀请流程中的短信体系进行改造，将原来通过短信链接的方式改为APP内查看邀请码。

## 短信模板变更

### 业务ID: 60372 (家庭账户邀请短信)

#### 原模板内容：
```
${custName}邀请您加入家庭账户，您可点击${URL}，查看邀请码及相关说明。
```

#### 新模板内容：
```
${custName}邀请您加入家庭账户，邀请码有效期30分钟。您可登录好买基金APP，通过"我的-私募基金-关联账户"，查看邀请码及相关说明。
```

#### 变更说明：
1. **移除URL参数** - 不再需要${URL}参数，避免短信链接被拦截
2. **增加有效期说明** - 明确告知邀请码有效期为30分钟
3. **增加APP入口指引** - 详细说明通过APP查看邀请码的路径

#### 参数说明：
- `${custName}`: 邀请人的客户姓名

## 代码变更说明

### 1. 新增接口
- `/simu/relatedaccount/queryinviteentry.htm` - 查询邀请码查看入口

### 2. 修改接口
- `sendInviteUrl()` 方法 - 移除短链接生成逻辑，直接发送纯文本短信

### 3. 新增DTO
- `InviteEntryDto` - 邀请码查看入口信息

## 业务流程变更

### 改造前流程：
1. 家庭主发送邀请
2. 系统生成短链接
3. 发送含链接的短信
4. 被邀请人点击短信链接
5. 跳转到H5页面查看邀请码

### 改造后流程：
1. 家庭主发送邀请
2. 发送纯文本短信（无链接）
3. 被邀请人收到短信
4. 被邀请人登录APP
5. 进入关联账户管理
6. 显示邀请码查看入口
7. 点击查看邀请码
8. APP内查看邀请码

## 邀请码查看入口展示规则

### 显示条件：
1. 登录用户存在**未过期**的家庭账户关联邀请，显示入口
2. 登录用户存在**过期**的家庭账户关联邀请，过期时间=当天，显示入口
3. 其他情况，隐藏入口

### 优先级规则：
如有多条邀请，仅取一条：未过期 > 已过期。如有多条，仅取最近的一条

### 入口文案：
```
图标 + XXX邀请您加入家庭账户 + [立即查看]按钮
```
- XXX：取邀请人的客户姓名，红色字显示
- 按键名称：立即查看

### 提示标签：
当家庭关联账户模块显示邀请码查看入口时，"私募基金-关联账户管理"的右上角显示提示标签。

## 注意事项

1. **短信模板需要运营配置** - 业务ID 60372的模板内容需要在短信平台进行配置
2. **兼容性考虑** - 新版本发布前，确保APP端已支持新的邀请码查看入口
3. **测试验证** - 需要验证短信发送成功率，确保无链接短信不被拦截
4. **用户体验** - 需要在APP中提供清晰的邀请码查看路径指引
