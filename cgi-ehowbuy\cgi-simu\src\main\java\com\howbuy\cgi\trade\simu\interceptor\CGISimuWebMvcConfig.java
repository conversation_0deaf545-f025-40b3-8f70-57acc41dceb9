package com.howbuy.cgi.trade.simu.interceptor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

/**
 * interceptor for simu
 */
@Configuration
@EnableWebMvc
public class CGISimuWebMvcConfig extends WebMvcConfigurerAdapter {

	@Autowired
	private CustLoginStatusInterceptor loginStatusInterceptor;
	
	@Autowired
	private CustRiskTestAndCertStatusInterceptor custRiskTestAndCertStatusInterceptor;

	@Autowired
	private CertStatusInterceptor certStatusInterceptor;

	@Autowired
	private CustRiskTestInterceptor custRiskTestInterceptor;

	@Autowired
	private CustTxpwdStatusInterceptor custTxpwdStatusInterceptor;

	@Autowired
	private SystemExceptionMonitorInterceptor systemExceptionMonitorInterceptor;

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		
		registry.addInterceptor(loginStatusInterceptor).addPathPatterns("/simu/*.htm")
		.excludePathPatterns("/simu/query/filedownload.htm")
			.excludePathPatterns("/simu/query/filedownloadInProcess.htm")
			.excludePathPatterns("/simu/esign/supplementaryEContractSign.htm");

		registry.addInterceptor(certStatusInterceptor)
				.addPathPatterns("/simu/*.htm")
				.addPathPatterns("/simu/*/*.htm")
				.excludePathPatterns("/simu/acctmgt/*.htm")
				.excludePathPatterns("/simu/query/filedownloadInProcess.htm")
				.excludePathPatterns("/simu/esign/supplementaryEContractSign.htm")
				.excludePathPatterns("/simu/trade/waitSupSignAgreementList.htm")
				.excludePathPatterns("/simu/relatedaccount/queryinvitecode.htm")
				.excludePathPatterns("/simu/relatedaccount/signagreement.htm")
				.excludePathPatterns("/simu/trade/uploadassetscertificate.htm");

		registry.addInterceptor(custRiskTestInterceptor)
				.addPathPatterns("/simu/*.htm")
				.addPathPatterns("/simu/*/*.htm")
				.excludePathPatterns("/simu/trade/buy.htm")
				.excludePathPatterns("/simu/acctmgt/*.htm")
				.excludePathPatterns("/simu/query/filedownloadInProcess.htm")
				.excludePathPatterns("/simu/esign/supplementaryEContractSign.htm")
				.excludePathPatterns("/simu/trade/uploadassetscertificate.htm")
				.excludePathPatterns("/simu/trade/getconcifgsetuplist.htm")
                .excludePathPatterns("/simu/relatedaccount/queryinvitecode.htm")
                .excludePathPatterns("/simu/relatedaccount/signagreement.htm");

		registry.addInterceptor(custTxpwdStatusInterceptor)
				 .addPathPatterns("/simu/trade/buy.htm")
		         .addPathPatterns("/simu/trade/sellStepOne.htm")
				 .addPathPatterns("/simu/trade/bonussettinglist.htm")
				 .addPathPatterns("/simu/trade/cancelconfirm.htm")
				.addPathPatterns("/simu/trade/bonussettinglistV2.htm");

		// 系统异常监控拦截器 - 对所有simu请求生效，用于监控非业务异常
		registry.addInterceptor(systemExceptionMonitorInterceptor)
				.addPathPatterns("/simu/*.htm")
				.addPathPatterns("/simu/*/*.htm");
//
//		registry.addInterceptor(custRiskTestAndCertStatusInterceptor)
//				.addPathPatterns("/simu/*.htm")
//				.addPathPatterns("/simu/*/*.htm")
//				.excludePathPatterns("/simu/acctmgt/*.htm")
//				.excludePathPatterns("/simu/query/filedownloadInProcess.htm")
//				.excludePathPatterns("/simu/esign/supplementaryEContractSign.htm")
//				.excludePathPatterns("/simu/trade/uploadassetscertificate.htm");
	}

}
