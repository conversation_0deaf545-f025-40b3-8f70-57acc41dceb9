package com.howbuy.cgi.trade.simu.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @Author: yun.lu
 * Date: 2023/5/23 14:11
 */
@Configuration
public class ThreadPoolConfig {

    @Bean
    @Primary
    public ThreadPoolExecutor threadPoolExecutor() {
        int cpuCoreNum = Runtime.getRuntime().availableProcessors();
        ThreadPoolExecutor threadPoolExecutor =  new ThreadPoolExecutor(cpuCoreNum + 1, 2 * cpuCoreNum, 10, TimeUnit.SECONDS, new LinkedBlockingDeque<>(500), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());
        // 拒绝策略,当前线程执行,默认不销毁
        threadPoolExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return threadPoolExecutor;
    }
}
